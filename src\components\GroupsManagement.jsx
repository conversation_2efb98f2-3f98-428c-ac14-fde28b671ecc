import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>s, FiPlus, FiTrash2, FiEdit, FiUserPlus, FiUserX,
  FiSettings, FiAlertTriangle, FiInfo, FiUserCheck, FiShield
} from 'react-icons/fi';
import { FaMobileAlt, FaUserShield, FaUserCog, FaUserEdit } from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import styles from './GroupsManagement.module.css';
import { getActiveAccount } from '../services/StorageService';
import {
  getGroups,
  getMembers,
  createGroup,
  deleteGroup,
  addMember,
  deleteMember
} from '../services/GroupsService';

const GroupsManagement = ({ currentUser }) => {
  const [activeTab, setActiveTab] = useState('groups');
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  const [activeAccount, setActiveAccount] = useState(getActiveAccount());
  const [loading, setLoading] = useState(true);

  // حالة المجموعات والأعضاء
  const [groups, setGroups] = useState([]);
  const [members, setMembers] = useState([]);
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [newMemberRole, setNewMemberRole] = useState('member');
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [formError, setFormError] = useState('');

  useEffect(() => {
    // تحديث حالة الحساب عند تغييرها
    const checkAccountStatus = () => {
      setActiveAccount(getActiveAccount());
    };

    window.addEventListener('storage', checkAccountStatus);

    return () => {
      window.removeEventListener('storage', checkAccountStatus);
    };
  }, []);

  // جلب بيانات المجموعات والأعضاء من Firebase
  useEffect(() => {
    const fetchData = async () => {
      if (currentUser && activeAccount === 'online') {
        setLoading(true);
        try {
          // جلب المجموعات
          const groupsData = await getGroups(currentUser.uid);
          setGroups(groupsData);

          // جلب الأعضاء
          const membersData = await getMembers();
          setMembers(membersData);
        } catch (error) {
          console.error('خطأ في جلب البيانات:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchData();
  }, [currentUser, activeAccount]);

  // فتح نافذة إضافة عضو
  const handleAddMember = () => {
    setFormError('');
    setNewMemberEmail('');
    setNewMemberRole('member');
    setShowAddMemberModal(true);
  };

  // فتح نافذة إنشاء مجموعة
  const handleCreateGroup = () => {
    setFormError('');
    setNewGroupName('');
    setNewGroupDescription('');
    setShowCreateGroupModal(true);
  };

  // إنشاء مجموعة جديدة
  const handleSubmitCreateGroup = async (e) => {
    e.preventDefault();

    if (!newGroupName.trim()) {
      setFormError('يرجى إدخال اسم المجموعة');
      return;
    }

    if (activeAccount !== 'online') {
      setFormError('يمكن إنشاء المجموعات فقط في وضع الاتصال بالإنترنت');
      return;
    }

    try {
      setLoading(true);

      // إنشاء بيانات المجموعة
      const groupData = {
        name: newGroupName.trim(),
        description: newGroupDescription.trim(),
        creatorName: currentUser.displayName || 'المستخدم الحالي',
        creatorEmail: currentUser.email || 'غير متوفر'
      };

      // إنشاء المجموعة في Firebase
      const newGroup = await createGroup(currentUser.uid, groupData);

      // تحديث حالة المجموعات
      setGroups([...groups, newGroup]);

      // جلب الأعضاء المحدثة
      const updatedMembers = await getMembers();
      setMembers(updatedMembers);

      setShowCreateGroupModal(false);
      setNewGroupName('');
      setNewGroupDescription('');
    } catch (error) {
      console.error('خطأ في إنشاء المجموعة:', error);
      setFormError('حدث خطأ أثناء إنشاء المجموعة. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  // إضافة عضو جديد
  const handleSubmitAddMember = async (e) => {
    e.preventDefault();

    if (!newMemberEmail.trim()) {
      setFormError('يرجى إدخال البريد الإلكتروني للعضو');
      return;
    }

    if (!selectedGroup && groups.length > 0) {
      setFormError('يرجى اختيار مجموعة');
      return;
    }

    if (activeAccount !== 'online') {
      setFormError('يمكن إضافة الأعضاء فقط في وضع الاتصال بالإنترنت');
      return;
    }

    const groupId = selectedGroup || (groups.length > 0 ? groups[0].id : null);

    if (!groupId) {
      setFormError('يرجى إنشاء مجموعة أولاً');
      return;
    }

    try {
      setLoading(true);

      // إنشاء بيانات العضو
      const memberData = {
        groupId: groupId,
        userId: Date.now().toString(), // استخدام معرف مؤقت للمستخدم الجديد
        name: newMemberEmail.split('@')[0], // استخدام جزء من البريد الإلكتروني كاسم مؤقت
        email: newMemberEmail.trim(),
        role: newMemberRole
      };

      // إضافة العضو في Firebase
      await addMember(memberData);

      // تحديث البيانات
      const updatedGroups = await getGroups(currentUser.uid);
      setGroups(updatedGroups);

      const updatedMembers = await getMembers();
      setMembers(updatedMembers);

      setShowAddMemberModal(false);
      setNewMemberEmail('');
      setNewMemberRole('member');
    } catch (error) {
      console.error('خطأ في إضافة العضو:', error);
      setFormError('حدث خطأ أثناء إضافة العضو. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  // حذف مجموعة
  const handleDeleteGroup = async (groupId) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المجموعة؟')) {
      if (activeAccount !== 'online') {
        alert('يمكن حذف المجموعات فقط في وضع الاتصال بالإنترنت');
        return;
      }

      try {
        setLoading(true);

        // حذف المجموعة من Firebase
        await deleteGroup(groupId);

        // تحديث حالة المجموعات والأعضاء
        setGroups(groups.filter(group => group.id !== groupId));
        setMembers(members.filter(member => member.groupId !== groupId));
      } catch (error) {
        console.error('خطأ في حذف المجموعة:', error);
        alert('حدث خطأ أثناء حذف المجموعة. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    }
  };

  // حذف عضو
  const handleDeleteMember = async (memberId) => {
    if (window.confirm('هل أنت متأكد من إزالة هذا العضو؟')) {
      if (activeAccount !== 'online') {
        alert('يمكن حذف الأعضاء فقط في وضع الاتصال بالإنترنت');
        return;
      }

      const memberToDelete = members.find(member => member.id === memberId);

      if (memberToDelete) {
        try {
          setLoading(true);

          // حذف العضو من Firebase
          await deleteMember(memberId, memberToDelete.groupId);

          // تحديث البيانات
          const updatedGroups = await getGroups(currentUser.uid);
          setGroups(updatedGroups);

          const updatedMembers = await getMembers();
          setMembers(updatedMembers);
        } catch (error) {
          console.error('خطأ في حذف العضو:', error);
          alert('حدث خطأ أثناء حذف العضو. يرجى المحاولة مرة أخرى.');
        } finally {
          setLoading(false);
        }
      }
    }
  };

  if (!currentUser) {
    return <div>جاري التحقق من حالة المستخدم...</div>;
  }

  return (
    <div className={styles.container}>
      <TopBar currentUser={currentUser} />
      <div className={styles.content}>
        <h1 className={styles.title}>
          <FiUsers className={styles.icon} />
          إدارة المجموعات
        </h1>

        {activeAccount === 'local' && (
          <div className={styles.offlineAlert}>
            <FiAlertTriangle className={styles.alertIcon} />
            <div className={styles.alertContent}>
              <h3>ميزة غير متاحة في الوضع المحلي</h3>
              <p>إدارة المجموعات متاحة فقط عند استخدام حساب أونلاين. يرجى التبديل إلى الوضع الأونلاين من صفحة الملف الشخصي للوصول إلى هذه الميزة.</p>
              <div className={styles.accountIndicator}>
                <FaMobileAlt className={styles.localIcon} />
                <span>أنت حاليًا تستخدم حساب محلي</span>
              </div>
            </div>
          </div>
        )}

        {activeAccount === 'online' && (
          <div className={styles.dashboard}>
            <div className={styles.sidebar}>
              <div className={styles.tabs}>
                <button
                  className={`${styles.tabButton} ${activeTab === 'groups' ? styles.activeTab : ''}`}
                  onClick={() => setActiveTab('groups')}
                >
                  <FiUsers className={styles.tabIcon} />
                  <span>المجموعات</span>
                </button>
                <button
                  className={`${styles.tabButton} ${activeTab === 'members' ? styles.activeTab : ''}`}
                  onClick={() => setActiveTab('members')}
                >
                  <FiUserPlus className={styles.tabIcon} />
                  <span>الأعضاء</span>
                </button>
                <button
                  className={`${styles.tabButton} ${activeTab === 'permissions' ? styles.activeTab : ''}`}
                  onClick={() => setActiveTab('permissions')}
                >
                  <FiSettings className={styles.tabIcon} />
                  <span>الصلاحيات</span>
                </button>
              </div>

              <div className={styles.statsContainer}>
                <div className={styles.statCard}>
                  <div className={styles.statIcon}>
                    <FiUsers />
                  </div>
                  <div className={styles.statInfo}>
                    <span className={styles.statValue}>{groups.length}</span>
                    <span className={styles.statLabel}>المجموعات</span>
                  </div>
                </div>

                <div className={styles.statCard}>
                  <div className={styles.statIcon}>
                    <FiUserPlus />
                  </div>
                  <div className={styles.statInfo}>
                    <span className={styles.statValue}>{members.length}</span>
                    <span className={styles.statLabel}>الأعضاء</span>
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.mainContent}>
              {loading && (
                <div className={styles.loadingContainer}>
                  <div className={styles.loadingSpinner}></div>
                  <p>جاري تحميل البيانات...</p>
                </div>
              )}

              {!loading && activeTab === 'groups' && (
                <div className={styles.section}>
                  <div className={styles.sectionHeader}>
                    <h2>
                      <FiUsers className={styles.headerIcon} />
                      إدارة المجموعات
                    </h2>
                    <button className={styles.addButton} onClick={handleCreateGroup}>
                      <FiPlus /> إنشاء مجموعة جديدة
                    </button>
                  </div>

                  {groups.length > 0 ? (
                    <div className={styles.groupsList}>
                      {groups.map(group => (
                        <div key={group.id} className={styles.groupCard}>
                          <div className={styles.groupHeader}>
                            <h3>{group.name}</h3>
                            <div className={styles.groupBadge}>
                              {group.members} {group.members === 1 ? 'عضو' : 'أعضاء'}
                            </div>
                          </div>

                          {group.description && (
                            <p className={styles.groupDescription}>{group.description}</p>
                          )}

                          <div className={styles.groupMeta}>
                            <span>تاريخ الإنشاء: {group.created}</span>
                          </div>

                          <div className={styles.groupActions}>
                            <button
                              className={styles.actionButton}
                              onClick={() => {
                                setSelectedGroup(group.id);
                                handleAddMember();
                              }}
                            >
                              <FiUserPlus /> إضافة عضو
                            </button>
                            <button
                              className={styles.deleteButton}
                              onClick={() => handleDeleteGroup(group.id)}
                            >
                              <FiTrash2 /> حذف
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className={styles.emptyState}>
                      <div className={styles.emptyStateIcon}>
                        <FiUsers />
                      </div>
                      <h3>لا توجد مجموعات حالياً</h3>
                      <p>قم بإنشاء مجموعة جديدة للبدء في إدارة فريقك</p>
                      <button className={styles.emptyStateButton} onClick={handleCreateGroup}>
                        <FiPlus /> إنشاء مجموعة جديدة
                      </button>
                    </div>
                  )}
                </div>
              )}

              {!loading && activeTab === 'members' && (
                <div className={styles.section}>
                  <div className={styles.sectionHeader}>
                    <h2>
                      <FiUserPlus className={styles.headerIcon} />
                      إدارة الأعضاء
                    </h2>
                    <button
                      className={styles.addButton}
                      onClick={handleAddMember}
                      disabled={groups.length === 0}
                    >
                      <FiUserPlus /> إضافة عضو جديد
                    </button>
                  </div>

                  {members.length > 0 ? (
                    <div className={styles.membersContainer}>
                      <div className={styles.filterBar}>
                        <div className={styles.filterGroup}>
                          <label>تصفية حسب المجموعة:</label>
                          <select
                            className={styles.filterSelect}
                            onChange={(e) => setSelectedGroup(e.target.value)}
                            value={selectedGroup || ''}
                          >
                            <option value="">جميع المجموعات</option>
                            {groups.map(group => (
                              <option key={group.id} value={group.id}>{group.name}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <table className={styles.membersTable}>
                        <thead>
                          <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>المجموعة</th>
                            <th>الدور</th>
                            <th>الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {members
                            .filter(member => !selectedGroup || member.groupId === selectedGroup)
                            .map(member => {
                              const group = groups.find(g => g.id === member.groupId);
                              const roleBadgeClass =
                                member.role === 'admin'
                                  ? styles.adminBadge
                                  : member.role === 'editor'
                                    ? styles.editorBadge
                                    : styles.memberBadge;

                              return (
                                <tr key={member.id}>
                                  <td>{member.name}</td>
                                  <td>{member.email}</td>
                                  <td>{group ? group.name : 'غير معروف'}</td>
                                  <td>
                                    <span className={`${styles.roleBadge} ${roleBadgeClass}`}>
                                      {member.role === 'admin'
                                        ? 'مدير'
                                        : member.role === 'editor'
                                          ? 'محرر'
                                          : 'عضو'}
                                    </span>
                                  </td>
                                  <td>
                                    <div className={styles.memberActions}>
                                      <button
                                        className={styles.deleteButton}
                                        onClick={() => handleDeleteMember(member.id)}
                                      >
                                        <FiUserX /> إزالة
                                      </button>
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className={styles.emptyState}>
                      <div className={styles.emptyStateIcon}>
                        <FiUserPlus />
                      </div>
                      <h3>لا يوجد أعضاء حالياً</h3>
                      {groups.length > 0 ? (
                        <>
                          <p>قم بإضافة أعضاء جدد للبدء في إدارة فريقك</p>
                          <button className={styles.emptyStateButton} onClick={handleAddMember}>
                            <FiUserPlus /> إضافة عضو جديد
                          </button>
                        </>
                      ) : (
                        <>
                          <p>قم بإنشاء مجموعة أولاً قبل إضافة الأعضاء</p>
                          <button className={styles.emptyStateButton} onClick={handleCreateGroup}>
                            <FiPlus /> إنشاء مجموعة جديدة
                          </button>
                        </>
                      )}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'permissions' && (
                <div className={styles.section}>
                  <div className={styles.sectionHeader}>
                    <h2>
                      <FiSettings className={styles.headerIcon} />
                      إعدادات الصلاحيات
                    </h2>
                  </div>

                  <div className={styles.permissionsContainer}>
                    <div className={styles.permissionCard}>
                      <div className={styles.permissionHeader}>
                        <h3>مدير (Admin)</h3>
                      </div>
                      <ul className={styles.permissionList}>
                        <li>إنشاء وحذف المجموعات</li>
                        <li>إضافة وإزالة الأعضاء</li>
                        <li>تعديل صلاحيات الأعضاء</li>
                        <li>الوصول الكامل لجميع البيانات</li>
                      </ul>
                    </div>

                    <div className={styles.permissionCard}>
                      <div className={styles.permissionHeader}>
                        <h3>محرر (Editor)</h3>
                      </div>
                      <ul className={styles.permissionList}>
                        <li>تعديل بيانات المجموعة</li>
                        <li>إضافة أعضاء جدد</li>
                        <li>تعديل البيانات والمحتوى</li>
                        <li>لا يمكنه حذف المجموعات</li>
                      </ul>
                    </div>

                    <div className={styles.permissionCard}>
                      <div className={styles.permissionHeader}>
                        <h3>عضو (Member)</h3>
                      </div>
                      <ul className={styles.permissionList}>
                        <li>عرض بيانات المجموعة</li>
                        <li>المشاركة في الأنشطة</li>
                        <li>تعديل بياناته الشخصية فقط</li>
                        <li>صلاحيات محدودة للقراءة فقط</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* نافذة إضافة عضو */}
      {showAddMemberModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3><FiUserPlus className={styles.modalIcon} /> إضافة عضو جديد</h3>
            </div>

            {formError && (
              <div className={styles.formError}>
                <FiAlertTriangle /> {formError}
              </div>
            )}

            <form onSubmit={handleSubmitAddMember}>
              <div className={styles.formGroup}>
                <label>البريد الإلكتروني</label>
                <input
                  type="email"
                  placeholder="أدخل البريد الإلكتروني للعضو"
                  value={newMemberEmail}
                  onChange={(e) => setNewMemberEmail(e.target.value)}
                  required
                />
              </div>

              {groups.length > 0 && (
                <div className={styles.formGroup}>
                  <label>المجموعة</label>
                  <select
                    value={selectedGroup || ''}
                    onChange={(e) => setSelectedGroup(e.target.value)}
                    required
                  >
                    <option value="" disabled>اختر المجموعة</option>
                    {groups.map(group => (
                      <option key={group.id} value={group.id}>{group.name}</option>
                    ))}
                  </select>
                </div>
              )}

              <div className={styles.formGroup}>
                <label>الدور</label>
                <select
                  value={newMemberRole}
                  onChange={(e) => setNewMemberRole(e.target.value)}
                >
                  <option value="admin">مدير</option>
                  <option value="editor">محرر</option>
                  <option value="member">عضو</option>
                </select>

                <div className={styles.roleDescription}>
                  {newMemberRole === 'admin' && (
                    <p>المدير لديه صلاحيات كاملة لإدارة المجموعات والأعضاء</p>
                  )}
                  {newMemberRole === 'editor' && (
                    <p>المحرر يمكنه تعديل المحتوى وإضافة أعضاء جدد</p>
                  )}
                  {newMemberRole === 'member' && (
                    <p>العضو لديه صلاحيات محدودة للقراءة والمشاركة فقط</p>
                  )}
                </div>
              </div>

              <div className={styles.modalButtons}>
                <button
                  type="button"
                  className={styles.cancelButton}
                  onClick={() => setShowAddMemberModal(false)}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  className={styles.submitButton}
                >
                  إضافة العضو
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة إنشاء مجموعة */}
      {showCreateGroupModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3><FiUsers className={styles.modalIcon} /> إنشاء مجموعة جديدة</h3>
            </div>

            {formError && (
              <div className={styles.formError}>
                <FiAlertTriangle /> {formError}
              </div>
            )}

            <form onSubmit={handleSubmitCreateGroup}>
              <div className={styles.formGroup}>
                <label>اسم المجموعة</label>
                <input
                  type="text"
                  placeholder="أدخل اسم المجموعة"
                  value={newGroupName}
                  onChange={(e) => setNewGroupName(e.target.value)}
                  required
                />
              </div>

              <div className={styles.formGroup}>
                <label>الوصف</label>
                <textarea
                  placeholder="أدخل وصفًا للمجموعة (اختياري)"
                  value={newGroupDescription}
                  onChange={(e) => setNewGroupDescription(e.target.value)}
                ></textarea>
              </div>

              <div className={styles.infoBox}>
                <p>سيتم إضافتك تلقائيًا كمدير لهذه المجموعة.</p>
              </div>

              <div className={styles.modalButtons}>
                <button
                  type="button"
                  className={styles.cancelButton}
                  onClick={() => setShowCreateGroupModal(false)}
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  className={styles.submitButton}
                >
                  إنشاء المجموعة
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroupsManagement;