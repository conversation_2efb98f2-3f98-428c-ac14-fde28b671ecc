:root {
  /* لوحة الألوان الحمراء الإضافية */
  --red-primary: #622872; /* بنفسجي داكن */
  --red-secondary: #caa5cb; /* وردي فاتح */
  --red-light: #e8ddea; /* وردي باهت */
  --red-lightest: #faeaf6; /* وردي فاتح جداً */

  /* لوحة الألوان الزرقاء الإضافية */
  --blue-darkest: #00033a; /* أزرق داكن جداً */
  --blue-dark-alt: #162647; /* أزرق داكن بديل */
  --blue-medium-alt: #163473; /* أزرق متوسط بديل */
  --accent-gold: #d2ab17; /* ذهبي */
}

/* --- General Page Structure --- */
.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

.mainContainer {
  flex-grow: 1;
  padding: 20px 25px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}







.noEvents {
  text-align: center;
  padding: 50px 20px;
  font-size: 1.1rem;
  color: #546e7a;
  background-color: #fff;
  border-radius: 8px;
  border: 1px dashed #bbdefb;
  margin-top: 30px;
}

/* --- Radar SVG Styles --- */
.radarContainer {
  position: relative;
  width: 100%;
  margin: 30px auto;
  border: 0.5px solid #c1c1c138;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: visible;
}

.radarSvg {
  display: block;
  width: 100%;
  height: auto;
  font-family: 'Tajawal', sans-serif;
  background: rgba(255, 255, 255, 0); /* تغيير الشفافية عند الـ hover */
  backdrop-filter: blur(20px); /* تقليل الـ blur أكتر عند الـ hover */

}

/* Time Rings */
.radarRingCircle {
  stroke: #878787;
  stroke-width: 1;
  stroke-dasharray: 6, 12;
  fill: none;
}

/* Center Point */
.centerGroup {}
.centerLabelSvg {
  font-size: 11px;
  font-weight: 600;
  fill: #ffffff;
  text-anchor: middle;
  dominant-baseline: middle;
}

/* Event Group & Dot */
.eventGroup {}

.eventDotCircle {
  cursor: pointer;
  transition: r 0.2s ease-out, filter 0.2s ease-out, opacity 0.3s ease; /* أضفنا transition للشفافية */
  stroke: #ffffff;
  stroke-width: 1;
  opacity: 3; /* شفافية أساسية للنقاط */

  /* تطبيق تأثير الـ glassmorphism */
  fill-opacity: 0.3; /* شفافية التعبئة */
  backdrop-filter: blur(50px); /* تأثير الـ blur */
  -webkit-backdrop-filter: blur(1px);
  filter: contrast(1.2) brightness(1.3); /* تحسين التباين والإضاءة */

}

.eventDotCircle:hover {
  r: 10;
  filter: brightness(1.2);
  opacity: 0.9; /* زيادة الشفافية عند الـ hover */
  backdrop-filter: blur(3px); /* تقليل الـ blur عند الـ hover */
  -webkit-backdrop-filter: blur(3px);
  fill-opacity: 0.8; /* شفافية التعبئة */
}

/* Client Name Text */
.clientNameText {
  font-size: 9px;
  fill: #f0f0f0;
  text-anchor: middle;
  dominant-baseline: central;
  pointer-events: none;
  user-select: none;
  font-weight: 500;
}

/* Connection Lines */
.connectionLine {
  stroke-width: 1.5;
  stroke-dasharray: 4, 3;
  opacity: 0.7;
}

/* --- Simple Tooltip Styles --- */
.simpleTooltip {
  position: absolute;
  z-index: 1000;
  min-width: 200px;
  max-width: 250px;
  color: #fff;
  padding: 10px;
  font-size: 0.85rem;
  line-height: 1.4;
  transform: translate(-50%, -50%) translateY(-10px);
  opacity: 10;
  cursor: pointer;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
  border-radius: 12px;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.simpleTooltip.visible {
  opacity: 0.95;
  transform: translate(-50%, -50%) translateY(0);
}

.simpleTooltip.left {
  transform: translate(-50%, -50%) translateY(-10px);
}
.simpleTooltip.left.visible {
  transform: translate(-50%, -50%) translateY(0);
}

/* --- Responsive Adjustments --- */
@media (max-width: 768px) {
  .mainContainer {
    padding: 0px;
  }
  .pageHeader {
    flex-direction: column-reverse;
    align-items: flex-end;
    gap: 10px;
    margin-bottom: 20px;
  }
  .backButton {
    padding: 6px 12px;
    font-size: 0.85rem;
  }
  .pageTitle {
    font-size: 1.4rem;
    width: 100%;
    text-align: right;
  }
  .radarContainer {
    margin: 10px 0; /* تقليل الـ margin عشان ياخد مساحة أكبر */
    width: 100%; /* التأكد إن العرض كامل */
  }
  .centerLabelSvg {
    font-size: 10px;
  }
  .eventDotCircle {
    r: 8;
  }
  .eventDotCircle:hover {
    r: 9;
  }
  .clientNameText {
    font-size: 9px;
  }
  .connectionLine {
    stroke-width: 1;
  }
  .simpleTooltip {
    min-width: 180px;
    max-width: 90vw;
    font-size: 0.8rem;
  }
  .connectionLine {
    stroke-width: 1.2; /* زيادة سماكة الخطوط */
  }
  .simpleTooltip {
    min-width: 200px; /* زيادة حجم الـ tooltip */
    max-width: 90vw;
    font-size: 0.9rem; /* زيادة حجم النص */
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 1.2rem;
  }
  .clientNameText {
    font-size: 8px;
    display: block;
  }
  .centerLabelSvg {
    font-size: 11px;
  }
  .eventDotCircle {
    r: 6; /* زيادة حجم النقاط */
  }
  .eventDotCircle:hover {
    r: 8;
  }
  .simpleTooltip {
    min-width: 180px;
    font-size: 0.85rem;
  }
  .mainContainer {
    padding: -10px; /* تقليل الـ padding عشان الرادار ياخد مساحة أكبر */
  }
}