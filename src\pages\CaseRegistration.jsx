import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import TopBar from "../components/topbar/TopBar";
import { caseDegrees, caseCategoriesByDegree, courtLocations, caseStatuses } from "../utils/CaseFilters";
import './CaseRegistration.css';
import { db } from '../config/firebaseConfig';
import { collection, addDoc, query, where, getDocs } from "firebase/firestore";
import { getActiveAccount, addCase, getCases, updateCase } from '../services/StorageService';

const CaseRegistration = ({ casesList = [], setCasesList = () => {}, currentUser }) => {
  console.log('CaseRegistration component rendered');

  const currentYear = new Date().getFullYear().toString();

  const [caseData, setCaseData] = useState({
    caseNumber: '',
    caseYear: currentYear,
    clientName: '',
    caseDescription: '',
    caseCategory: '',
    opponentName: '',
    caseDegree: '',
    circleNumber: '',
    courtLocation: '',
    caseStatus: 'قيد النظر',
    reportNumber: '',
    reportLocation: '',
    caseDate: '', // تاريخ رفع الدعوى أو تاريخ المحضر
    originalCaseId: '', // معرف القضية الأصلية
    originalCaseDegree: '', // درجة القضية الأصلية
    originalCaseNumber: '' // رقم القضية الأصلية
  });

  const [fullCaseNumber, setFullCaseNumber] = useState('');
  const [isDegreeModified, setIsDegreeModified] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isTransferCase, setIsTransferCase] = useState(false);
  const navigate = useNavigate();

  // تحميل البيانات المنقولة من تحويل الدرجة
  useEffect(() => {
    const transferData = localStorage.getItem('transferData');
    if (transferData) {
      try {
        const parsedData = JSON.parse(transferData);
        setCaseData(prev => ({
          ...prev,
          clientName: parsedData.clientName || '',
          caseDescription: parsedData.caseDescription || '',
          opponentName: parsedData.opponentName || '',
          caseCategory: parsedData.caseCategory || '',
          courtLocation: parsedData.courtLocation || '',
          caseStatus: 'دعوى قضائية', // تحويل الدرجة يعني دعوى قضائية
          originalCaseId: parsedData.originalCaseId || '',
          originalCaseDegree: parsedData.originalCaseDegree || '',
          originalCaseNumber: parsedData.originalCaseNumber || '',
          originalCourtLocation: parsedData.originalCourtLocation || '',
          originalCaseDate: parsedData.originalCaseDate || '',
          // حفظ البيانات الأساسية الأصلية
          originalClientName: parsedData.originalClientName || '',
          originalOpponentName: parsedData.originalOpponentName || '',
          originalCaseDescription: parsedData.originalCaseDescription || '',
          originalCaseCategory: parsedData.originalCaseCategory || '',
          originalCircleNumber: parsedData.originalCircleNumber || ''
        }));
        setIsTransferCase(true);
        // مسح البيانات من localStorage بعد التحميل
        localStorage.removeItem('transferData');
      } catch (error) {
        console.error('خطأ في تحميل بيانات التحويل:', error);
      }
    }
  }, []);

  useEffect(() => {
    if (!isDegreeModified && caseData.courtLocation) {
      const selectedCourt = courtLocations.find(court => court.name === caseData.courtLocation);
      if (selectedCourt) {
        setCaseData(prev => ({ ...prev, caseDegree: selectedCourt.degree }));
      }
    }
  }, [caseData.courtLocation, isDegreeModified]);

  useEffect(() => {
    if (caseData.caseDegree && caseCategoriesByDegree[caseData.caseDegree]) {
      if (!caseCategoriesByDegree[caseData.caseDegree].includes(caseData.caseCategory)) {
        setCaseData(prev => ({
          ...prev,
          caseCategory: caseCategoriesByDegree[caseData.caseDegree][0]
        }));
      }
    }
  }, [caseData.caseDegree]);

  useEffect(() => {
    if (caseData.caseStatus === 'دعوى قضائية') {
      setFullCaseNumber(
        caseData.caseNumber ? `${caseData.caseNumber}/${caseData.caseYear}` : caseData.caseYear
      );
    } else if (caseData.caseStatus === 'محضر') {
      setFullCaseNumber(
        caseData.reportNumber ? `${caseData.reportNumber}/${caseData.caseYear}` : caseData.caseYear
      );
    } else {
      setFullCaseNumber('');
    }
  }, [caseData.caseNumber, caseData.caseYear, caseData.reportNumber, caseData.caseStatus]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCaseData(prev => ({ ...prev, [name]: value }));
    if (name === 'caseDegree') setIsDegreeModified(true);
  };

  // دالة لتحديث الأرشيف الزمني الموحد للملفات المحولة
  const updateSharedTimeline = async (originalCaseId, newCaseId, newCaseDegree, caseDate, newCaseNumber) => {
    try {
      console.log('بدء تحديث الأرشيف الزمني الموحد...');
      console.log('معرف القضية الأصلية:', originalCaseId);
      console.log('معرف القضية الجديدة:', newCaseId);
      console.log('الدرجة الجديدة:', newCaseDegree);
      console.log('تاريخ القضية:', caseDate);
      console.log('رقم القضية الجديد:', newCaseNumber);

      // الحصول على جميع القضايا للعثور على الملفات المرتبطة
      const allCases = await getCases(currentUser.uid);
      console.log('إجمالي القضايا:', allCases.length);
      console.log('البحث عن القضية الأصلية بمعرف:', originalCaseId);
      console.log('قائمة معرفات جميع القضايا:', allCases.map(c => c.id));

      // البحث عن القضية الأصلية أولاً
      const originalCase = allCases.find(c => c.id === originalCaseId);

      // البحث عن جميع القضايا المرتبطة
      const relatedCases = allCases.filter(c =>
        c.originalCaseId === originalCaseId ||  // القضايا المحولة من نفس الأصل
        c.id === originalCaseId ||              // القضية الأصلية نفسها
        c.id === newCaseId                      // القضية الجديدة المحولة
      );

      console.log('القضايا المرتبطة الموجودة:', relatedCases.map(c => ({ id: c.id, degree: c.caseDegree, number: c.fullCaseNumber })));

      if (relatedCases.length === 0) {
        console.warn('لم يتم العثور على قضايا مرتبطة للتحديث');
        console.log('سيتم تحديث القضية الجديدة فقط:', newCaseId);

        // إذا لم توجد قضايا مرتبطة، نحدث القضية الجديدة فقط
        const newCase = allCases.find(c => c.id === newCaseId);
        if (newCase) {
          relatedCases.push(newCase);
        }
      }

      // تحديد نص الحدث حسب الدرجة الجديدة
      let actionText = '';
      switch (newCaseDegree) {
        case 'استئنافي':
          actionText = 'تم رفع استئناف';
          break;
        case 'نقض':
          actionText = 'تم رفع نقض';
          break;
        case 'إعادة نظر':
          actionText = 'تم رفع إعادة نظر';
          break;
        default:
          actionText = `تم تحويل الدرجة إلى ${newCaseDegree}`;
      }

      // إنشاء حدث جديد للأرشيف الزمني الموحد (استخدام history بدلاً من timeline)
      const historyEntry = {
        type: 'degree_transfer',
        timestamp: new Date().toISOString(),
        action: `${actionText} بتاريخ ${new Date(caseDate).toLocaleDateString('ar-EG')} برقم ${newCaseNumber}`,
        date: new Date(caseDate).toLocaleDateString('ar-EG'),
        transferDate: caseDate,
        newCaseNumber: newCaseNumber,
        newCaseDegree: newCaseDegree,
        relatedCaseId: newCaseId,
        isSharedHistory: true,
        // حفظ البيانات القديمة للمقارنة لاحقاً
        oldData: {
          clientName: originalCase?.clientName,
          opponentName: originalCase?.opponentName,
          caseDescription: originalCase?.caseDescription,
          caseCategory: originalCase?.caseCategory,
          courtLocation: originalCase?.courtLocation,
          caseDegree: originalCase?.caseDegree,
          fullCaseNumber: originalCase?.fullCaseNumber,
          caseDate: originalCase?.caseDate,
          circleNumber: originalCase?.circleNumber
        }
      };

      // تحديث الأرشيف الزمني لجميع القضايا المرتبطة
      console.log('بدء تحديث', relatedCases.length, 'قضايا مرتبطة...');

      const updatePromises = relatedCases.map(async (caseData) => {
        console.log('تحديث القضية:', caseData.id, '-', caseData.fullCaseNumber);

        // إنشاء أرشيف أولي إذا لم يكن موجوداً (للقضايا القديمة)
        let initialHistory = [...(caseData.history || [])];

        // إذا لم يكن هناك أرشيف، أنشئ إدخال واحد فقط حسب نوع القضية
        if (initialHistory.length === 0) {
          if (caseData.caseStatus === 'محضر' && caseData.caseDate) {
            // للمحضر: حدث واحد فقط بتاريخ المحضر
            const reportEntry = {
              type: 'report_created',
              timestamp: new Date(caseData.caseDate).toISOString(),
              action: `تم تحرير المحضر بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
              date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
              reportDate: caseData.caseDate
            };
            initialHistory.push(reportEntry);
          } else if (caseData.caseStatus === 'دعوى قضائية' && caseData.caseDate) {
            // للدعوى: حدث واحد فقط بتاريخ رفع الدعوى
            const lawsuitEntry = {
              type: 'lawsuit_created',
              timestamp: new Date(caseData.caseDate).toISOString(),
              action: `تم رفع دعوى قضائية بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
              date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
              lawsuitDate: caseData.caseDate
            };
            initialHistory.push(lawsuitEntry);
          } else {
            // لقيد النظر أو القضايا بدون تاريخ: حدث إنشاء عادي
            const creationEntry = {
              type: 'case_created',
              timestamp: caseData.createdAt || new Date().toISOString(),
              action: `تم إنشاء ${caseData.caseStatus}`,
              date: new Date(caseData.createdAt || new Date()).toLocaleDateString('ar-EG')
            };
            initialHistory.push(creationEntry);
          }
        }

        const updatedHistory = [...initialHistory, historyEntry];
        const updatedCaseData = {
          ...caseData,
          history: updatedHistory,
          updatedAt: new Date().toISOString()
        };

        console.log('الأرشيف الزمني الجديد للقضية', caseData.id, ':', updatedHistory.length, 'أحداث');
        console.log('الأحداث الأصلية:', caseData.history?.length || 0);
        console.log('الأحداث الأولية (مع الإنشاء):', initialHistory.length);
        console.log('الأحداث النهائية (مع تحويل الدرجة):', updatedHistory.length);

        return updateCase(currentUser.uid, caseData.id, updatedCaseData);
      });

      await Promise.all(updatePromises);
      console.log('تم تحديث الأرشيف الزمني الموحد لجميع الملفات المرتبطة بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث الأرشيف الزمني الموحد:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (!currentUser) {
      alert("يجب تسجيل الدخول لحفظ القضايا.");
      navigate('/login');
      return;
    }

    if (!caseData.clientName.trim()) {
      setError('يجب إدخال اسم الموكل');
      return;
    }

    let generatedFullCaseNumber = '';
    if (caseData.caseStatus === 'قيد النظر') {
      generatedFullCaseNumber = `قيد النظر-${Date.now()}`;
    } else if (caseData.caseStatus === 'محضر') {
      if (!caseData.reportNumber.trim()) {
        setError('يجب إدخال رقم المحضر.');
        return;
      }
      if (!caseData.caseYear || !/^\d{4}$/.test(caseData.caseYear)) {
        setError('السنة يجب أن تكون مكونة من 4 أرقام.');
        return;
      }
      generatedFullCaseNumber = `${caseData.reportNumber.trim()}/${caseData.caseYear}`;
    } else if (caseData.caseStatus === 'دعوى قضائية') {
      if (!caseData.caseNumber.trim()) {
        setError('يجب إدخال رقم القضية.');
        return;
      }
      if (!caseData.caseYear || !/^\d{4}$/.test(caseData.caseYear)) {
        setError('السنة يجب أن تكون مكونة من 4 أرقام.');
        return;
      }
      generatedFullCaseNumber = `${caseData.caseNumber.trim()}/${caseData.caseYear}`;
    }

    // التحقق من تكرار رقم القضية حسب الحساب النشط
    const activeAccount = getActiveAccount();

    if (activeAccount === 'online') {
      // التحقق من تكرار رقم القضية في Firestore
      try {
        const casesQuery = query(
          collection(db, 'cases'),
          where('userId', '==', currentUser.uid),
          where('fullCaseNumber', '==', generatedFullCaseNumber)
        );
        const querySnapshot = await getDocs(casesQuery);
        if (!querySnapshot.empty) {
          setError('رقم القضية موجود بالفعل! يرجى اختيار رقم قضية مختلف.');
          return;
        }
      } catch (e) {
        setError('حدث خطأ أثناء التحقق من رقم القضية: ' + e.message);
        return;
      }
    } else {
      // التحقق من تكرار رقم القضية في التخزين المحلي
      const localCases = await getCases(currentUser.uid);
      const duplicateCase = localCases.find(c => c.fullCaseNumber === generatedFullCaseNumber);
      if (duplicateCase) {
        setError('رقم القضية موجود بالفعل في الحساب المحلي! يرجى اختيار رقم قضية مختلف.');
        return;
      }
    }

    if (caseData.caseStatus === 'دعوى قضائية') {
      if (!caseData.circleNumber.trim()) { setError('يجب إدخال رقم الدائرة.'); return; }
      if (!caseData.caseDegree) { setError('يجب اختيار درجة الدعوى.'); return; }
      if (!caseData.caseCategory) { setError('يجب اختيار نوع الدعوى.'); return; }
      if (!caseData.courtLocation) { setError('يجب اختيار مكان المحكمة.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال الوصف.'); return; }
      if (!caseData.caseDate.trim()) { setError('يجب إدخال تاريخ رفع الدعوى.'); return; }
    }

    if (caseData.caseStatus === 'محضر') {
      if (!caseData.reportLocation.trim()) { setError('يجب إدخال مكان الجهة المختصة.'); return; }
      if (!caseData.reportNumber.trim()) { setError('يجب إدخال رقم المحضر.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال وصف المحضر.'); return; }
      if (!caseData.caseDate.trim()) { setError('يجب إدخال تاريخ المحضر.'); return; }
    }

    if (caseData.caseStatus === 'قيد النظر') {
      if (!caseData.reportLocation.trim()) { setError('يجب إدخال مكان الجهة المختصة.'); return; }
      if (!caseData.caseDescription.trim()) { setError('يجب إدخال الوصف القضائي.'); return; }
    }

    const confirmSave = window.confirm('هل أنت متأكد من حفظ القضية؟');
    if (!confirmSave) {
      return;
    }

    // إنشاء أرشيف أولي للقضية الجديدة ونسخ بيانات المحضر
    let initialHistory = [];
    let originalReportData = {}; // لحفظ بيانات المحضر من القضية الأصلية

    // إذا كانت قضية تحويل درجة، نسخ الأحداث وبيانات المحضر من القضية الأصلية
    if (isTransferCase && caseData.originalCaseId) {
      try {
        // الحصول على القضية الأصلية
        const allCases = await getCases(currentUser.uid);
        const originalCase = allCases.find(c => c.id === caseData.originalCaseId);

        if (originalCase) {
          // نسخ بيانات المحضر إذا كانت موجودة
          if (originalCase.reportNumber || originalCase.reportLocation) {
            originalReportData = {
              reportNumber: originalCase.reportNumber,
              reportLocation: originalCase.reportLocation,
              reportYear: originalCase.caseYear // سنة المحضر
            };
            console.log('تم نسخ بيانات المحضر:', originalReportData);
          }

          if (originalCase.history && originalCase.history.length > 0) {
            // نسخ جميع الأحداث من القضية الأصلية
            initialHistory = [...originalCase.history];
            console.log('تم نسخ', initialHistory.length, 'أحداث من القضية الأصلية');
          } else {
            // إنشاء أرشيف أولي للقضية الأصلية إذا لم يكن لديها أرشيف
            if (originalCase.caseStatus === 'محضر' && originalCase.caseDate) {
              const reportEntry = {
                type: 'report_created',
                timestamp: new Date(originalCase.caseDate).toISOString(),
                action: `تم تحرير المحضر بتاريخ ${new Date(originalCase.caseDate).toLocaleDateString('ar-EG')}`,
                date: new Date(originalCase.caseDate).toLocaleDateString('ar-EG'),
                reportDate: originalCase.caseDate
              };
              initialHistory.push(reportEntry);
            } else if (originalCase.caseStatus === 'دعوى قضائية' && originalCase.caseDate) {
              const lawsuitEntry = {
                type: 'lawsuit_created',
                timestamp: new Date(originalCase.caseDate).toISOString(),
                action: `تم رفع دعوى قضائية بتاريخ ${new Date(originalCase.caseDate).toLocaleDateString('ar-EG')}`,
                date: new Date(originalCase.caseDate).toLocaleDateString('ar-EG'),
                lawsuitDate: originalCase.caseDate
              };
              initialHistory.push(lawsuitEntry);
            }
          }
        }
      } catch (error) {
        console.error('خطأ في نسخ الأحداث وبيانات المحضر من القضية الأصلية:', error);
      }
    }

    // إضافة حدث القضية الجديدة
    if (caseData.caseStatus === 'دعوى قضائية' && caseData.caseDate) {
      // للدعوى: حدث رفع الدعوى الجديدة
      const lawsuitEntry = {
        type: 'lawsuit_created',
        timestamp: new Date(caseData.caseDate).toISOString(),
        action: `تم رفع دعوى قضائية بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
        date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
        lawsuitDate: caseData.caseDate
      };
      initialHistory.push(lawsuitEntry);
    } else if (!isTransferCase) {
      // للقضايا الجديدة غير المحولة
      if (caseData.caseStatus === 'محضر' && caseData.caseDate) {
        const reportEntry = {
          type: 'report_created',
          timestamp: new Date(caseData.caseDate).toISOString(),
          action: `تم تحرير المحضر بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
          date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
          reportDate: caseData.caseDate
        };
        initialHistory.push(reportEntry);
      } else {
        const creationEntry = {
          type: 'case_created',
          timestamp: new Date().toISOString(),
          action: `تم إنشاء ${caseData.caseStatus}`,
          date: new Date().toLocaleDateString('ar-EG')
        };
        initialHistory.push(creationEntry);
      }
    }

    const caseDataToSave = {
      fullCaseNumber: generatedFullCaseNumber,
      caseNumber: caseData.caseStatus === 'دعوى قضائية' ? caseData.caseNumber.trim() : null,
      caseYear: caseData.caseStatus === 'قيد النظر' ? currentYear : caseData.caseYear,
      clientName: caseData.clientName.trim(),
      opponentName: caseData.opponentName.trim() || null,
      caseDescription: caseData.caseDescription.trim() || null,
      caseCategory: caseData.caseCategory || null,
      caseDegree: caseData.caseDegree || null,
      courtLocation: caseData.courtLocation || null,
      circleNumber: caseData.caseStatus === 'دعوى قضائية' ? caseData.circleNumber.trim() || null : null,
      caseDate: (caseData.caseStatus === 'دعوى قضائية' || caseData.caseStatus === 'محضر') ? caseData.caseDate.trim() || null : null,
      caseStatus: caseData.caseStatus || 'قيد النظر',
      reportNumber: caseData.caseStatus === 'محضر' ? caseData.reportNumber.trim() || null : originalReportData.reportNumber || null,
      reportLocation: (caseData.caseStatus === 'محضر' || caseData.caseStatus === 'قيد النظر') ? caseData.reportLocation.trim() || null : originalReportData.reportLocation || null,
      reportYear: originalReportData.reportYear || null, // إضافة سنة المحضر
      deferrals: [],
      actions: [],
      history: initialHistory,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: currentUser.uid,
      // بيانات تحويل الدرجة
      originalCaseId: caseData.originalCaseId || null,
      originalCaseDegree: caseData.originalCaseDegree || null,
      originalCaseNumber: caseData.originalCaseNumber || null,
      originalCourtLocation: caseData.originalCourtLocation || null,
      originalCaseDate: caseData.originalCaseDate || null,
      // حفظ البيانات الأساسية الأصلية للمقارنة
      originalClientName: caseData.originalClientName || null,
      originalOpponentName: caseData.originalOpponentName || null,
      originalCaseDescription: caseData.originalCaseDescription || null,
      originalCaseCategory: caseData.originalCaseCategory || null,
      originalCircleNumber: caseData.originalCircleNumber || null,
      isTransferredCase: isTransferCase
    };

    setLoading(true);
    try {
      // استخدام خدمة التخزين المحلي لإضافة القضية حسب الحساب النشط
      const savedCase = await addCase(currentUser.uid, caseDataToSave);
      console.log("Case saved with ID: ", savedCase.id);
      console.log('الأرشيف الأولي للقضية الجديدة:', initialHistory.length, 'أحداث');
      console.log('بيانات المحضر المنسوخة:', originalReportData);
      console.log('البيانات المحفوظة النهائية:', caseDataToSave);

      // إذا كانت قضية تحويل درجة، تحديث الأرشيف الزمني الموحد للملفات المرتبطة
      if (isTransferCase && caseData.originalCaseId && caseData.caseDegree && caseData.caseDate) {
        console.log('شروط تحديث الأرشيف متوفرة:');
        console.log('- isTransferCase:', isTransferCase);
        console.log('- originalCaseId:', caseData.originalCaseId);
        console.log('- caseDegree:', caseData.caseDegree);
        console.log('- caseDate:', caseData.caseDate);
        console.log('- generatedFullCaseNumber:', generatedFullCaseNumber);

        await updateSharedTimeline(
          caseData.originalCaseId,
          savedCase.id,
          caseData.caseDegree,
          caseData.caseDate,
          generatedFullCaseNumber
        );
      } else {
        console.log('شروط تحديث الأرشيف غير متوفرة:');
        console.log('- isTransferCase:', isTransferCase);
        console.log('- originalCaseId:', caseData.originalCaseId);
        console.log('- caseDegree:', caseData.caseDegree);
        console.log('- caseDate:', caseData.caseDate);
      }

      setCaseData({
        caseNumber: '',
        caseYear: currentYear,
        clientName: '',
        caseDescription: '',
        caseCategory: '',
        opponentName: '',
        caseDegree: '',
        circleNumber: '',
        courtLocation: '',
        caseStatus: 'قيد النظر',
        reportNumber: '',
        reportLocation: '',
        caseDate: ''
      });
      setFullCaseNumber('');
      setIsDegreeModified(false);

      // عرض رسالة نجاح مختلفة حسب الحساب النشط ونوع العملية
      if (isTransferCase) {
        setSuccess('تم تحويل الدرجة القضائية بنجاح وتحديث الأرشيف الزمني الموحد!');
      } else if (activeAccount === 'online') {
        setSuccess('تم حفظ القضية بنجاح في الحساب الأونلاين!');
      } else {
        setSuccess('تم حفظ القضية بنجاح في الحساب المحلي!');
      }

      setTimeout(() => {
        navigate(`/case-details/${savedCase.id}`);
      }, 1500);
    } catch (e) {
      console.error("Error saving case: ", e);
      setError("حدث خطأ أثناء حفظ القضية: " + e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard', { state: { refresh: true } }); // تمرير حالة لتحديث البيانات
  };

  return (
    <div>
      <TopBar currentUser={currentUser} casesList={casesList} />
      <div className="container">
        <div className="case-registration-container">
          <h2>{isTransferCase ? 'تحويل درجة قضائية' : 'تسجيل قضية جديدة'}</h2>
          {isTransferCase && (
            <div className="transfer-info">
              <p>تحويل من: <strong>{caseData.originalCaseDegree}</strong> - رقم: <strong>{caseData.originalCaseNumber}</strong></p>
            </div>
          )}
          {error && (
            <div className="error-message">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginLeft: '8px' }}>
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
              {error}
            </div>
          )}
          {success && (
            <div className="success-message">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginLeft: '8px' }}>
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
              {success}
            </div>
          )}
          {loading && (
            <div className="loading-container">
              <div className="spinner"></div>
              <p className="loading-text">جاري حفظ القضية...</p>
            </div>
          )}

          <form className="case-form" onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="input-group">
                <label>الحالة:</label>
                <select
                  name="caseStatus"
                  value={caseData.caseStatus}
                  onChange={handleChange}
                  className="select-field"
                >
                  {caseStatuses.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>
              {caseData.caseStatus === 'دعوى قضائية' && (
                <div className="input-group">
                  <label>رقم القضية: <span className="required-star">*</span></label>
                  <div className="case-number-inputs">
                    <input
                      type="text"
                      name="caseNumber"
                      value={caseData.caseNumber}
                      onChange={handleChange}
                      placeholder="0000"
                      className="case-number-input"
                    />
                    <span className="separator">/</span>
                    <input
                      type="text"
                      name="caseYear"
                      value={caseData.caseYear}
                      onChange={handleChange}
                      placeholder="2025"
                      className="case-year-input"
                    />
                  </div>
                  {fullCaseNumber && caseData.caseStatus === 'دعوى قضائية' && (
                    <div className="generated-number">
                      رقم القضية الكامل: <strong>{fullCaseNumber}</strong>
                    </div>
                  )}
                </div>
              )}
              {caseData.caseStatus === 'محضر' && (
                <div className="input-group">
                  <label>رقم المحضر: <span className="required-star">*</span></label>
                  <div className="case-number-inputs">
                    <input
                      type="text"
                      name="reportNumber"
                      value={caseData.reportNumber}
                      onChange={handleChange}
                      placeholder="0000"
                      className="case-number-input"
                    />
                    <span className="separator">/</span>
                    <input
                      type="text"
                      name="caseYear"
                      value={caseData.caseYear}
                      onChange={handleChange}
                      placeholder="2025"
                      className="case-year-input"
                    />
                  </div>
                  {fullCaseNumber && caseData.caseStatus === 'محضر' && (
                    <div className="generated-number">
                      رقم المحضر الكامل: <strong>{fullCaseNumber}</strong>
                    </div>
                  )}
                </div>
              )}
              {caseData.caseStatus === 'قيد النظر' && (
                <div className="input-group empty-group" />
              )}
            </div>

            <div className="form-row">
              <div className="input-group">
                <label>اسم الموكل: <span className="required-star">*</span></label>
                <input
                  type="text"
                  name="clientName"
                  value={caseData.clientName}
                  onChange={handleChange}
                />
              </div>
              <div className="input-group">
                <label>اسم الخصم (اختياري):</label>
                <input
                  type="text"
                  name="opponentName"
                  value={caseData.opponentName}
                  onChange={handleChange}
                />
              </div>
            </div>

            {caseData.caseStatus === 'قيد النظر' && (
              <>
                <div className="form-row">
                  <div className="input-group">
                    <label>مكان الجهة: <span className="required-star">*</span></label>
                    <input
                      type="text"
                      name="reportLocation"
                      value={caseData.reportLocation}
                      onChange={handleChange}
                      placeholder="أدخل مكان الجهة المختصة"
                    />
                  </div>
                </div>
                <div className="form-row">
                  <div className="input-group input-group-full">
                    <label>الوصف: <span className="required-star">*</span></label>
                    <input
                      type="text"
                      name="caseDescription"
                      value={caseData.caseDescription}
                      onChange={handleChange}
                      placeholder="أدخل وصف القضية"
                    />
                  </div>
                </div>
              </>
            )}

            {caseData.caseStatus === 'محضر' && (
              <>
                <div className="form-row">
                  <div className="input-group">
                    <label>مكان الجهة: <span className="required-star">*</span></label>
                    <input
                      type="text"
                      name="reportLocation"
                      value={caseData.reportLocation}
                      onChange={handleChange}
                      placeholder="أدخل مكان الجهة المختصة"
                    />
                  </div>
                  <div className="input-group empty-group" />
                </div>
                <div className="form-row">
                  <div className="input-group">
                    <label>تاريخ المحضر: <span className="required-star">*</span></label>
                    <input
                      type="date"
                      name="caseDate"
                      value={caseData.caseDate}
                      onChange={handleChange}
                      className="date-input"
                    />
                  </div>
                  <div className="input-group empty-group" />
                </div>
                <div className="form-row">
                  <div className="input-group input-group-full">
                    <label>الوصف: <span className="required-star">*</span></label>
                    <input
                      type="text"
                      name="caseDescription"
                      value={caseData.caseDescription}
                      onChange={handleChange}
                      placeholder="أدخل وصف المحضر"
                    />
                  </div>
                </div>
              </>
            )}

            {caseData.caseStatus === 'دعوى قضائية' && (
              <>
                <div className="form-row">
                  <div className="input-group">
                    <label>رقم الدائرة: <span className="required-star">*</span></label>
                    <input
                      type="text"
                      name="circleNumber"
                      value={caseData.circleNumber}
                      onChange={handleChange}
                      placeholder="أدخل رقم الدائرة"
                    />
                  </div>
                  <div className="input-group">
                    <label>المحكمة: <span className="required-star">*</span></label>
                    <select
                      name="courtLocation"
                      value={caseData.courtLocation}
                      onChange={handleChange}
                      className="select-field"
                    >
                      <option value="">اختر المحكمة</option>
                      {courtLocations.map(court => <option key={court.name} value={court.name}>{court.name}</option>)}
                    </select>
                  </div>
                </div>

                <div className="form-row">
                  <div className="input-group">
                    <label>تاريخ رفع الدعوى: <span className="required-star">*</span></label>
                    <input
                      type="date"
                      name="caseDate"
                      value={caseData.caseDate}
                      onChange={handleChange}
                      className="date-input"
                    />
                  </div>
                  <div className="input-group empty-group" />
                </div>

                <div className="form-row">
                  <div className="input-group">
                    <label>الدرجة: <span className="required-star">*</span></label>
                    <select
                      name="caseDegree"
                      value={caseData.caseDegree}
                      onChange={handleChange}
                      className="select-field"
                    >
                      <option value="">اختر درجة الدعوى</option>
                      {caseDegrees.map(degree => <option key={degree} value={degree}>{degree}</option>)}
                    </select>
                    <small className="helper-text">
                      {isDegreeModified ? "تم تعديل الدرجة يدوياً" : "سيتم تعيين الدرجة تلقائياً حسب المحكمة"}
                    </small>
                  </div>
                  <div className="input-group">
                    <label>النوع: <span className="required-star">*</span></label>
                    <select
                      name="caseCategory"
                      value={caseData.caseCategory}
                      onChange={handleChange}
                      disabled={!caseData.caseDegree}
                      className="select-field"
                    >
                      <option value="">اختر نوع الدعوى</option>
                      {caseData.caseDegree && caseCategoriesByDegree[caseData.caseDegree]?.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                </div>



                <div className="form-row">
                  <div className="input-group input-group-full">
                    <label>الوصف: <span className="required-star">*</span></label>
                    <input
                      type="text"
                      name="caseDescription"
                      value={caseData.caseDescription}
                      onChange={handleChange}
                      placeholder="أدخل وصف الدعوى القضائية"
                    />
                  </div>
                </div>
              </>
            )}

            <div className="button-group">
              <button type="submit" className="case-save-btn" disabled={loading}>
                {loading ? 'جاري الحفظ...' : 'حفظ القضية'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="case-cancel-btn"
                disabled={loading}
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CaseRegistration;