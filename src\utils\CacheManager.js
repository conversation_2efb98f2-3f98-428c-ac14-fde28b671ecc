// مدير التخزين المؤقت والتحديثات الفورية
class CacheManager {
  constructor() {
    this.listeners = new Map();
    this.cacheKeys = {
      NOTIFICATIONS: 'notifications_',
      CASE_DETAILS: 'case_',
      DASHBOARD_STATS: 'dashboard_stats_'
    };
  }

  // إضافة مستمع للتحديثات
  addListener(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    this.listeners.get(key).add(callback);
  }

  // إزالة مستمع
  removeListener(key, callback) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).delete(callback);
    }
  }

  // إشعار جميع المستمعين بالتحديث
  notifyListeners(key, data) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in cache listener:', error);
        }
      });
    }
  }

  // مسح التخزين المؤقت لمفتاح معين
  clearCache(keyPattern) {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.includes(keyPattern)) {
        localStorage.removeItem(key);
      }
    });
  }

  // مسح جميع التخزين المؤقت المرتبط بالمستخدم
  clearUserCache(userId) {
    const patterns = [
      `${this.cacheKeys.NOTIFICATIONS}${userId}`,
      `${this.cacheKeys.CASE_DETAILS}`,
      `${this.cacheKeys.DASHBOARD_STATS}${userId}`
    ];
    
    patterns.forEach(pattern => this.clearCache(pattern));
  }

  // تحديث التخزين المؤقت وإشعار المستمعين
  updateCache(key, data, ttl = 30000) {
    const cacheData = {
      data,
      timestamp: Date.now(),
      ttl
    };
    
    localStorage.setItem(key, JSON.stringify(cacheData));
    this.notifyListeners(key, data);
  }

  // الحصول على البيانات من التخزين المؤقت
  getCache(key) {
    try {
      const cached = localStorage.getItem(key);
      if (!cached) return null;

      const { data, timestamp, ttl } = JSON.parse(cached);
      
      // التحقق من انتهاء صلاحية التخزين المؤقت
      if (Date.now() - timestamp > ttl) {
        localStorage.removeItem(key);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error reading cache:', error);
      localStorage.removeItem(key);
      return null;
    }
  }

  // إشعار بتحديث البيانات (للاستخدام بعد العمليات)
  notifyDataUpdate(type, userId, caseId = null) {
    switch (type) {
      case 'TASK_COMPLETED':
        // مسح تخزين الإشعارات والقضية المؤقت
        this.clearCache(`${this.cacheKeys.NOTIFICATIONS}${userId}`);
        if (caseId) {
          this.clearCache(`${this.cacheKeys.CASE_DETAILS}${caseId}`);
        }
        
        // إشعار المستمعين
        this.notifyListeners('notifications_refresh', { userId });
        this.notifyListeners('case_refresh', { caseId });
        break;
        
      case 'TASK_CREATED':
        // مسح تخزين الإشعارات المؤقت
        this.clearCache(`${this.cacheKeys.NOTIFICATIONS}${userId}`);
        this.notifyListeners('notifications_refresh', { userId });
        break;
        
      case 'CASE_UPDATED':
        // مسح تخزين القضية المؤقت
        if (caseId) {
          this.clearCache(`${this.cacheKeys.CASE_DETAILS}${caseId}`);
          this.notifyListeners('case_refresh', { caseId });
        }
        break;
    }
  }
}

// إنشاء مثيل واحد للاستخدام في جميع أنحاء التطبيق
export const cacheManager = new CacheManager();

// دوال مساعدة للاستخدام السهل
export const clearNotificationsCache = (userId) => {
  cacheManager.clearCache(`${cacheManager.cacheKeys.NOTIFICATIONS}${userId}`);
};

export const clearCaseCache = (caseId) => {
  cacheManager.clearCache(`${cacheManager.cacheKeys.CASE_DETAILS}${caseId}`);
};

export const notifyTaskCompleted = (userId, caseId) => {
  cacheManager.notifyDataUpdate('TASK_COMPLETED', userId, caseId);
};

export const notifyTaskCreated = (userId) => {
  cacheManager.notifyDataUpdate('TASK_CREATED', userId);
};

export const notifyCaseUpdated = (caseId) => {
  cacheManager.notifyDataUpdate('CASE_UPDATED', null, caseId);
};
