/* متغيرات CSS مخصصة - ثيم أزرق مهني */
:root {
  /* ألوان أساسية - لوحة زرقاء مهنية */
  --primary-color: #4c68c0; /* أزرق متوسط */
  --primary-light: #dad1dc; /* لافندر فاتح */
  --primary-dark: #2a2e70; /* أزرق داكن أساسي */

  /* ألوان ثانوية */
  --secondary-color: #555269; /* رمادي-أزرق داكن */
  --secondary-light: #dad1dc; /* لافندر فاتح */
  --secondary-dark: #2a2e70; /* أزرق داكن */

  /* ألوان الحالات */
  --success-color: #4c68c0; /* أزرق للنجاح */
  --warning-color: #555269; /* رمادي-أزرق للتحذير */
  --danger-color: #2a2e70; /* أزرق داكن للخطر */

  /* ألوان محايدة */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  /* ألوان حالة القضية - ألوان متنوعة */
  --case-pending: #163473; /* أزرق متوسط بديل */
  --case-report: #d2ab17; /* ذهبي للتقارير */
  --case-lawsuit: #622872; /* بنفسجي داكن للدعاوى */

  /* لوحة الألوان الحمراء الإضافية */
  --red-primary: #622872; /* بنفسجي داكن */
  --red-secondary: #caa5cb; /* وردي فاتح */
  --red-light: #e8ddea; /* وردي باهت */
  --red-lightest: #faeaf6; /* وردي فاتح جداً */

  /* لوحة الألوان الزرقاء الإضافية */
  --blue-darkest: #00033a; /* أزرق داكن جداً */
  --blue-dark-alt: #162647; /* أزرق داكن بديل */
  --blue-medium-alt: #163473; /* أزرق متوسط بديل */
  --accent-gold: #d2ab17; /* ذهبي */

  /* تأثيرات ناعمة */
  --border-radius-sm: 6px;
  --border-radius: 10px;
  --border-radius-lg: 14px;
  --border-radius-xl: 18px;

  --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
  --shadow: 0 2px 4px rgba(0,0,0,0.05);
  --shadow-md: 0 4px 8px rgba(0,0,0,0.05);
  --shadow-lg: 0 6px 12px rgba(0,0,0,0.05);
  --shadow-xl: 0 8px 16px rgba(0,0,0,0.05);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;

  /* حجم الخط */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
}

/* التخطيط العام */
.pageWrapper {
  min-height: 100vh;
  background: var(--neutral-50);
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--neutral-700);
}

.mainContainer {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* قسم العنوان - بأسلوب هادئ */
.headerSection {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow);
  position: relative;
  overflow: visible;
  transition: var(--transition);
  border-bottom: 3px solid var(--primary-light);
}

/* خانة الخبراء في أقصى اليسار والأعلى داخل الهدر */
.topLeftExpertBox {
  position: absolute;
  top: 20px;
  left: 20px;
  max-width: 350px;
  z-index: 10;
}

.headerSection:hover {
  box-shadow: var(--shadow-md);
}

/* شريط ملون حسب نوع القضية */
.headerSection.دعوىقضائية {
  border-bottom-color: var(--case-lawsuit);
}

.headerSection.محضر {
  border-bottom-color: var(--case-report);
}

.headerSection.قيدالنظر {
  border-bottom-color: var(--case-pending);
}

.headerContent {
  width: 100%;
}

.headerLayout {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1.5rem;
  width: 100%;
}

.titleSection {
  text-align: right;
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.headerExpertSection {
  flex: 0 0 auto;
  max-width: 400px;
}

.headerSpacer {
  flex: 1;
}

.caseTitle {
  font-size: var(--text-3xl);
  font-weight: 600;
  margin: 0;
  color: var(--neutral-700);
  text-align: right;
}

.caseTitle.قيدالنظر {
  color: var(--case-pending);
}

.caseTitle.محضر {
  color: var(--case-report);
}

.caseTitle.دعوىقضائية {
  color: var(--case-lawsuit);
}

.caseStatusIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.caseSubtitle {
  color: var(--neutral-600);
  font-size: var(--text-lg);
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  margin: 0;
  text-align: right;
}

.subtitleIcon {
  color: var(--secondary-color);
}

/* تخطيط المحتوى */
.contentGrid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.caseInfoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  width: 100%;
}

/* بطاقات المعلومات - بأسلوب هادئ */
.infoGroup {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.infoGroup:hover {
  box-shadow: var(--shadow-md);
}

.groupTitle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: var(--text-xl);
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--neutral-200);
}

.groupTitle svg {
  color: white;
  font-size: var(--text-lg);
  padding: 0.5rem;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

.titleText {
  flex: 1;
}

.infoItems {
  display: grid;
  gap: 1rem;
  flex: 1;
}

/* عناصر المعلومات - بأسلوب هادئ */
.infoItem {
  padding: 1rem;
  background: var(--neutral-50);
  border-radius: var(--border-radius);
  border: 1px solid var(--neutral-200);
  transition: var(--transition);
  position: relative;
}

.infoItem:hover {
  background: white;
  border-color: var(--primary-light);
}

.infoLabel {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--neutral-500);
  margin-bottom: 0.5rem;
  display: block;
}

.valueContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
}

.infoValue {
  font-size: var(--text-base);
  font-weight: 400;
  color: var(--neutral-700);
  flex: 1;
  word-break: break-word;
  line-height: 1.5;
}

/* زر التعديل - بأسلوب هادئ */
.editIconButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  opacity: 0;
  transform: scale(0.8);
}

.infoItem:hover .editIconButton {
  opacity: 1;
  transform: scale(1);
}

.editIconButton:hover {
  background: var(--primary-dark);
}

/* تنسيقات التعديل */
.editFieldContainer {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--primary-light);
}

.editInput, .textareaInput {
  padding: 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius);
  font-size: var(--text-base);
  transition: var(--transition);
  background: var(--neutral-50);
}

.editInput:focus, .textareaInput:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 2px rgba(120, 166, 200, 0.1);
}

.textareaInput {
  min-height: 100px;
  resize: vertical;
}

.inputError {
  border-color: var(--danger-color);
  background: #fff5f5;
}

.errorText {
  color: var(--danger-color);
  font-size: var(--text-sm);
  font-weight: 400;
  margin-top: 0.5rem;
}

.editActions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 1rem;
}




.addOptions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.followUpButton {
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 400;
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
  min-width: 180px;
  max-width: 250px;
}


 .followUpButton {
  background: var(--secondary-color);
  color: white;
}

followUpButton:hover {
  background: var(--secondary-dark);
}

.buttonIcon {
  font-size: var(--text-lg);
}

.buttonsSection {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1.5rem;
}

.backButton, .editButton, .saveButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 400;
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.backButton {
  background: var(--neutral-500);
  color: white;
}

.backButton:hover {
  background: var(--neutral-600);
}

.editButton {
  background: var(--warning-color);
  color: white;
}

.editButton:hover {
  background: #d4b179;
}

.saveButton {
  background: var(--success-color);
  color: white;
}

.saveButton:hover {
  background: #6ca372;
}

/* رسائل الخطأ والتحميل - بأسلوب هادئ */
.errorMessage {
  text-align: center;
  color: var(--danger-color);
  font-size: var(--text-lg);
  font-weight: 400;
  background: #fff5f5;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid #fdd;
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.errorIcon {
  font-size: 2rem;
  color: var(--danger-color);
}

.loadingMessage {
  text-align: center;
  color: var(--primary-color);
  font-size: var(--text-lg);
  font-weight: 400;
  background: var(--primary-light);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid #d4e6f9;
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

/* أيقونة التحميل الدوارة - بأسلوب هادئ */
.loadingIcon {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(120, 166, 200, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تنسيقات إضافية للنماذج */
.addFormContainer {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--neutral-200);
  margin: 1.5rem 0;
  position: relative;
  overflow: hidden;
}

.addFormContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-light);
}

/* تنسيقات الحوار */
.promptDialog {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--primary-light);
  margin: 1.5rem 0;
  text-align: center;
}

.promptDialog h3 {
  color: var(--neutral-700);
  font-size: var(--text-xl);
  font-weight: 500;
  margin-bottom: 1rem;
}

.previewDialog {
  background: var(--neutral-50);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem 0;
  text-align: right;
}

.previewDialog h4 {
  color: var(--neutral-700);
  font-size: var(--text-lg);
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.previewDialog ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.previewDialog li {
  margin-bottom: 0.5rem;
  padding-right: 1rem;
  position: relative;
}

.previewDialog li::before {
  content: '•';
  position: absolute;
  right: 0;
  color: var(--primary-color);
}

/* تأثيرات الحركة - بأسلوب هادئ */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.headerSection, .infoGroup, .actionsSection {
  animation: fadeIn 0.5s ease;
}

/* تحسينات للأجهزة اللوحية */
@media (max-width: 1024px) {
  .topLeftExpertBox {
    max-width: 300px;
    left: 15px;
    top: 15px;
  }

  .twoColumnLayout {
    grid-template-columns: 1fr 300px;
    gap: 1.5rem;
  }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .topLeftExpertBox {
    position: static;
    max-width: none;
    margin-bottom: 0;
    margin-top: 1rem;
    left: auto;
    top: auto;
    z-index: auto;
    order: 2;
  }

  .mainContainer {
    padding: 1rem;
    gap: 1rem;
  }

  .headerSection {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
  }

  .headerContent {
    order: 1;
  }

  .headerLayout {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .titleSection {
    width: 100%;
    text-align: center;
    align-items: center;
  }

  .headerExpertSection {
    width: 100%;
    max-width: none;
  }

  .caseTitle {
    font-size: var(--text-2xl);
    text-align: center;
  }

  .caseSubtitle {
    font-size: var(--text-base);
    justify-content: center;
    text-align: center;
  }

  .caseInfoGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .infoGroup {
    padding: 1rem;
  }

  .groupTitle {
    font-size: var(--text-lg);
    margin-bottom: 1rem;
  }

  .infoItem {
    padding: 0.75rem;
  }

  .addOptions {
    flex-direction: column;
    align-items: stretch;
  }

  .addDeferralButton, .addActionOptionButton, .followUpButton {
    max-width: none;
    width: 100%;
  }

  .buttonsSection {
    flex-direction: column;
    align-items: stretch;
  }

  .backButton, .editButton, .saveButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .topLeftExpertBox {
    position: static;
    max-width: none;
    margin-bottom: 0;
    margin-top: 0.75rem;
    left: auto;
    top: auto;
    order: 2;
  }

  .mainContainer {
    padding: 0.75rem;
  }

  .headerSection {
    padding: 1rem;
    display: flex;
    flex-direction: column;
  }

  .headerContent {
    order: 1;
  }

  .caseTitle {
    font-size: var(--text-xl);
  }

  .caseSubtitle {
    font-size: var(--text-sm);
  }

  .infoGroup {
    padding: 0.75rem;
  }

  .groupTitle {
    font-size: var(--text-base);
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .groupTitle svg {
    font-size: var(--text-base);
    padding: 0.4rem;
  }

  .infoItem {
    padding: 0.5rem;
  }

  .infoLabel {
    font-size: var(--text-xs);
  }

  .infoValue {
    font-size: var(--text-sm);
  }

  .editIconButton {
    width: 28px;
    height: 28px;
  }

  .addDeferralButton, .addActionOptionButton, .followUpButton {
    padding: 0.5rem 0.75rem;
    font-size: var(--text-sm);
  }

  .buttonIcon {
    font-size: var(--text-base);
  }
}
