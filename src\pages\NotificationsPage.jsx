import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  FaFileAlt,
  FaCalendarAlt,
  FaFileSignature,
  FaExclamationTriangle,
  FaFilter,
  FaBell,
  FaRegMehBlank,
  FaSpinner,
  FaGlobe,
  FaMobileAlt,
  FaCheck
} from 'react-icons/fa';
import styles from './NotificationsPage.module.css';
import TopBar from '../components/topbar/TopBar';
import { db } from '../config/firebaseConfig';
import { collection, query, where, getDocs } from "firebase/firestore";
import {
  getActiveAccount,
  getCases,
  saveLocalNotifications
} from '../services/StorageService';
import { handleCompleteAction, handleCompleteDeferral } from './CentralDetailsPage/ReportDetailsLogic';
import { cacheManager, notifyTaskCompleted } from '../utils/CacheManager';

// --- Constants ---
const CACHE_PREFIX = 'notifications_';
const CACHE_TTL = 30 * 1000; // 30 seconds for faster updates
const NOTIFICATION_TYPES = {
  DEFERRAL: 'تأجيل',
  ACTION: 'إجراء',
  UPCOMING_ACTION: 'إجراء قريب',
};
const PRIORITIES = {
  HIGH: 'high',
  MEDIUM: 'medium',
};

// --- Helper Functions ---
const isTomorrow = (date) => {
  const today = new Date();
  const tomorrow = new Date();
  tomorrow.setDate(today.getDate() + 1);

  // تعيين الوقت إلى منتصف الليل للمقارنة الصحيحة
  const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const tomorrowOnly = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate());

  console.log('🔍 isTomorrow check - تشخيص مفصل:', {
    inputDate: date.toDateString(),
    inputDateISO: date.toISOString(),
    dateOnly: dateOnly.toDateString(),
    dateOnlyISO: dateOnly.toISOString(),
    today: today.toDateString(),
    todayISO: today.toISOString(),
    tomorrow: tomorrow.toDateString(),
    tomorrowISO: tomorrow.toISOString(),
    tomorrowOnly: tomorrowOnly.toDateString(),
    tomorrowOnlyISO: tomorrowOnly.toISOString(),
    isEqual: dateOnly.getTime() === tomorrowOnly.getTime(),
    dateTime: dateOnly.getTime(),
    tomorrowTime: tomorrowOnly.getTime(),
    timeDifference: dateOnly.getTime() - tomorrowOnly.getTime(),
    daysDifference: Math.floor((dateOnly.getTime() - tomorrowOnly.getTime()) / (1000 * 60 * 60 * 24))
  });

  return dateOnly.getTime() === tomorrowOnly.getTime();
};

const isWithinThreeDays = (date) => {
  const today = new Date();
  const threeDaysFromNow = new Date();
  threeDaysFromNow.setDate(today.getDate() + 3);

  // تعيين الوقت إلى منتصف الليل للمقارنة الصحيحة
  const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const threeDaysOnly = new Date(threeDaysFromNow.getFullYear(), threeDaysFromNow.getMonth(), threeDaysFromNow.getDate());

  // التأكد من أن التاريخ في المستقبل وخلال الأيام الثلاثة القادمة
  return dateOnly > todayOnly && dateOnly <= threeDaysOnly;
};

// دالة للتحقق من شروط عرض التنبيه حسب نوع التذكير
const shouldShowNotification = (action, deadlineDate) => {
  const today = new Date();
  const dateOnly = new Date(deadlineDate.getFullYear(), deadlineDate.getMonth(), deadlineDate.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const createdDate = action.createdAt ? new Date(action.createdAt) : todayOnly;
  const createdDateOnly = new Date(createdDate.getFullYear(), createdDate.getMonth(), createdDate.getDate());

  console.log('🔍 shouldShowNotification - تشخيص مفصل:', {
    actionId: action.id,
    description: action.description,
    reminderType: action.reminderType,
    deadline: deadlineDate.toDateString(),
    deadlineISO: deadlineDate.toISOString(),
    today: todayOnly.toDateString(),
    todayISO: todayOnly.toISOString(),
    createdAt: createdDateOnly.toDateString(),
    createdAtISO: createdDateOnly.toISOString(),
    deadlineTime: dateOnly.getTime(),
    todayTime: todayOnly.getTime(),
    createdTime: createdDateOnly.getTime()
  });

  // إذا لم يكن هناك نوع تذكير محدد، استخدم المنطق القديم (قبل يوم واحد)
  if (!action.reminderType || action.reminderType === 'dayBefore') {
    const tomorrow = isTomorrow(deadlineDate);
    console.log('📅 dayBefore logic:', {
      deadline: deadlineDate.toDateString(),
      isTomorrow: tomorrow,
      shouldShow: tomorrow
    });
    return tomorrow;
  }

  // إذا كان النوع "إشعار يومي"، اعرض من تاريخ الإنشاء حتى موعد الإجراء
  if (action.reminderType === 'daily') {
    const isAfterCreation = todayOnly >= createdDateOnly;
    const isBeforeDeadline = todayOnly <= dateOnly;
    const result = isAfterCreation && isBeforeDeadline;
    console.log('📅 daily logic - تشخيص مفصل:', {
      todayTime: todayOnly.getTime(),
      createdTime: createdDateOnly.getTime(),
      deadlineTime: dateOnly.getTime(),
      isAfterCreation,
      isBeforeDeadline,
      result,
      daysDifferenceFromCreation: Math.floor((todayOnly.getTime() - createdDateOnly.getTime()) / (1000 * 60 * 60 * 24)),
      daysDifferenceToDeadline: Math.floor((dateOnly.getTime() - todayOnly.getTime()) / (1000 * 60 * 60 * 24))
    });
    return result;
  }

  console.log('❌ Unknown reminder type:', action.reminderType);
  return false;
};

const formatDate = (dateString) => {
  try {
    // Format date for display (e.g., "٢٨ أبريل ٢٠٢٥")
    return new Date(dateString).toLocaleDateString('ar-EG', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  } catch (e) {
    return 'تاريخ غير صالح'; // Fallback for invalid dates
  }
};


// --- Main Component ---
const NotificationsPage = ({ currentUser }) => {
  const [allNotifications, setAllNotifications] = useState([]);
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all'); // 'all', 'high', 'medium'
  const [activeAccount] = useState(getActiveAccount());

  const cacheKey = useMemo(() => `${CACHE_PREFIX}${currentUser?.uid || 'guest'}`, [currentUser]);

  // دالة لمعالجة إكمال الإجراء - محسنة للسرعة
  const handleActionComplete = async (notification) => {
    try {
      console.log('⚡ بدء معالجة إكمال الإجراء:', notification);

      // التحقق من وجود البيانات المطلوبة
      if (!notification.actionId || !notification.caseId) {
        console.error('❌ بيانات الإشعار غير مكتملة:', notification);
        alert('خطأ: بيانات الإشعار غير مكتملة');
        return;
      }

      // إزالة الإشعار من الواجهة فوراً لتحسين تجربة المستخدم
      setFilteredNotifications(prev => prev.filter(n => n.id !== notification.id));
      setAllNotifications(prev => prev.filter(n => n.id !== notification.id));

      // البحث عن القضية المرتبطة بالإشعار
      const caseData = await getCases(currentUser.uid);
      console.log('📊 تم جلب بيانات القضايا:', caseData.length);

      const relatedCase = caseData.find(c => c.id === notification.caseId);
      console.log('📁 القضية المرتبطة:', relatedCase?.fullCaseNumber);

      if (relatedCase) {
        // العثور على الإجراء المحدد
        const action = relatedCase.actions?.find(a => a.id === notification.actionId);
        console.log('📋 الإجراء المحدد:', action?.description);

        if (action) {
          console.log('⚡ بدء تنفيذ handleCompleteAction');
          await handleCompleteAction(
            notification.actionId,
            relatedCase.actions || [],
            () => {}, // setActions - لا نحتاجها هنا
            relatedCase,
            () => {} // setHistory - لا نحتاجها هنا
          );

          console.log('✅ تم تنفيذ الإجراء بنجاح');
          alert('تم تنفيذ الإجراء وإضافته للأرشيف الزمني');

          // إشعار مدير التخزين المؤقت بالتحديث
          notifyTaskCompleted(currentUser.uid, notification.caseId);

          // تحديث فوري للإشعارات
          fetchNotifications(true);
        } else {
          console.error('❌ لم يتم العثور على الإجراء المحدد');
          alert('خطأ: لم يتم العثور على الإجراء المحدد');
          // إعادة الإشعار إذا فشلت العملية
          await fetchNotifications(true);
        }
      } else {
        console.error('❌ لم يتم العثور على القضية المرتبطة');
        alert('خطأ: لم يتم العثور على القضية المرتبطة');
        // إعادة الإشعار إذا فشلت العملية
        await fetchNotifications(true);
      }
    } catch (error) {
      console.error('❌ خطأ في إكمال الإجراء:', error);
      alert('حدث خطأ أثناء تسجيل إكمال الإجراء: ' + error.message);
      // إعادة تحميل الإشعارات في حالة الخطأ
      await fetchNotifications(true);
    }
  };

  // دالة لمعالجة إكمال التأجيلة - محسنة للسرعة
  const handleDeferralComplete = async (notification) => {
    try {
      console.log('⚡ بدء معالجة إكمال التأجيلة:', notification);

      // إزالة الإشعار من الواجهة فوراً لتحسين تجربة المستخدم
      setFilteredNotifications(prev => prev.filter(n => n.id !== notification.id));
      setAllNotifications(prev => prev.filter(n => n.id !== notification.id));

      // البحث عن القضية المرتبطة بالإشعار
      const caseData = await getCases(currentUser.uid);
      console.log('📊 تم جلب بيانات القضايا:', caseData.length);

      const relatedCase = caseData.find(c => c.id === notification.caseId || c.fullCaseNumber === notification.caseNumber);
      console.log('📁 القضية المرتبطة:', relatedCase?.fullCaseNumber);

      if (relatedCase) {
        // استخراج index التأجيلة من id الإشعار
        const deferralIndex = parseInt(notification.id.split('-defer-')[1]);
        console.log('📋 فهرس التأجيلة:', deferralIndex);

        if (deferralIndex >= 0 && relatedCase.deferrals && deferralIndex < relatedCase.deferrals.length) {
          console.log('⚡ بدء تنفيذ handleCompleteDeferral');
          await handleCompleteDeferral(
            deferralIndex,
            relatedCase.deferrals || [],
            () => {}, // setDeferrals - لا نحتاجها هنا
            relatedCase,
            () => {} // setHistory - لا نحتاجها هنا
          );

          console.log('✅ تم إكمال التأجيلة بنجاح');
          alert('تم تسجيل حضور الجلسة وإضافتها للأرشيف الزمني');

          // إشعار مدير التخزين المؤقت بالتحديث
          notifyTaskCompleted(currentUser.uid, notification.caseId);

          // تحديث فوري للإشعارات
          fetchNotifications(true);
        } else {
          console.error('❌ فهرس التأجيلة غير صحيح');
          alert('خطأ: لم يتم العثور على التأجيلة المحددة');
          // إعادة الإشعار إذا فشلت العملية
          await fetchNotifications(true);
        }
      } else {
        console.error('❌ لم يتم العثور على القضية المرتبطة');
        alert('خطأ: لم يتم العثور على القضية المرتبطة');
        // إعادة الإشعار إذا فشلت العملية
        await fetchNotifications(true);
      }
    } catch (error) {
      console.error('❌ خطأ في إكمال التأجيلة:', error);
      alert('حدث خطأ أثناء تسجيل حضور الجلسة: ' + error.message);
      // إعادة تحميل الإشعارات في حالة الخطأ
      await fetchNotifications(true);
    }
  };

  // لا نحتاج إلى وظيفة تبديل الحساب هنا لأنها موجودة في صفحة البروفايل

  // --- Data Fetching Logic ---
  const fetchNotifications = useCallback(async (bypassCache = false) => {
    if (!currentUser) {
      setAllNotifications([]);
      setFilteredNotifications([]);
      setLoading(false);
      setError('الرجاء تسجيل الدخول لعرض الإشعارات.');
      return;
    }

    // 1. Try fetching from cache if not bypassing
    if (!bypassCache) {
      const cachedData = localStorage.getItem(cacheKey);
      if (cachedData) {
        try {
          const { data, timestamp, source } = JSON.parse(cachedData);
          // استخدم البيانات المخزنة مؤقتًا فقط إذا كانت من نفس مصدر الحساب النشط
          if (Date.now() - timestamp < CACHE_TTL && source === activeAccount) {
            setAllNotifications(data);
            // Apply filter immediately from cached data
            applyFilter(activeFilter, data);
            setLoading(false);
            return; // Cache is fresh, no need to fetch
          }
        } catch (e) {
          console.error("Error parsing cache:", e);
          localStorage.removeItem(cacheKey); // Clear corrupted cache
        }
      }
    }

    // 2. Fetch data based on active account
    setLoading(true);
    setError(null);

    try {
      const userId = currentUser.uid;
      let userCases = [];

      // الحصول على القضايا حسب الحساب النشط
      if (activeAccount === 'online') {
        // التحقق من الاتصال بالإنترنت للحساب الأونلاين
        if (!navigator.onLine) {
          setError('أنت غير متصل بالإنترنت حاليًا. يرجى الاتصال بالإنترنت أو التبديل إلى الحساب المحلي.');
          setLoading(false);
          return;
        }

        try {
          // الحصول على القضايا من Firestore
          const casesQuery = query(
            collection(db, 'cases'),
            where('userId', '==', userId)
          );
          const querySnapshot = await getDocs(casesQuery);
          userCases = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            source: 'online' // إضافة مصدر البيانات
          }));
        } catch (firebaseError) {
          console.error('خطأ في الاتصال بـ Firestore:', firebaseError);
          setError('حدث خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقًا أو التبديل إلى الحساب المحلي.');
          setLoading(false);
          return;
        }
      } else {
        // الحصول على القضايا من التخزين المحلي
        userCases = await getCases(userId);
        // إضافة مصدر البيانات لكل قضية
        userCases = userCases.map(caseItem => ({
          ...caseItem,
          source: 'local'
        }));
      }

      const generatedNotifications = [];
      console.log('عدد القضايا المجلبة:', userCases.length);

      userCases.forEach((caseItem) => {
        console.log(`🔍 فحص القضية: ${caseItem.fullCaseNumber}`, {
          deferralsCount: (caseItem.deferrals || []).length,
          actionsCount: (caseItem.actions || []).length,
          caseId: caseItem.id
        });

        // Deferrals Notifications (Tomorrow = High Priority)
        (caseItem.deferrals || []).forEach((deferral, index) => {
          console.log(`🔍 فحص تأجيلة ${index} في القضية ${caseItem.fullCaseNumber}:`, {
            date: deferral.date,
            reasons: deferral.reasons,
            isDeleted: deferral.isDeleted,
            hasDate: !!deferral.date,
            fullDeferral: deferral
          });

          if (deferral.isDeleted || !deferral.date) {
            console.log(`⏭️ تخطي التأجيلة ${index}: محذوفة أو بدون تاريخ`);
            return;
          }
          try {
            const deferralDate = new Date(deferral.date);
            console.log(`📅 تاريخ التأجيلة ${index}:`, {
              originalDate: deferral.date,
              parsedDate: deferralDate,
              dateString: deferralDate.toDateString(),
              isValidDate: !isNaN(deferralDate.getTime())
            });

            const tomorrow = isTomorrow(deferralDate);
            console.log(`✅ نتيجة فحص العرض للتأجيلة ${index}:`, tomorrow);

            if (tomorrow) {
              // إنشاء نص الأسباب مع الوصف إذا كان موجوداً
              let reasonsText = deferral.reasons?.join('، ') || 'لا يوجد سبب محدد';
              if (deferral.description && deferral.description.trim()) {
                reasonsText += ` - ${deferral.description}`;
              }

              const notification = {
                id: `${caseItem.id}-defer-${index}`,
                type: NOTIFICATION_TYPES.DEFERRAL,
                priority: PRIORITIES.HIGH,
                caseNumber: caseItem.fullCaseNumber || 'غير محدد',
                clientName: caseItem.clientName || 'غير محدد',
                courtLocation: caseItem.courtLocation || 'غير محددة',
                date: deferralDate, // Store Date object for sorting
                displayDate: formatDate(deferral.date),
                reasons: reasonsText, // الأسباب مع الوصف
                deferralDescription: deferral.description || '', // الوصف منفصل
                accountType: activeAccount, // إضافة نوع الحساب للإشعار
                caseId: caseItem.id, // إضافة معرف القضية
                deferralIndex: index, // إضافة فهرس التأجيلة
              };
              console.log(`🔔 إضافة إشعار تأجيلة:`, notification);
              generatedNotifications.push(notification);
            }
          } catch (e) {
            console.error(`❌ Invalid date for deferral ${index} in case ${caseItem.id}:`, deferral.date, e);
          }
        });

        // Actions Notifications - حسب نوع التذكير المختار
        // عرض التنبيهات النشطة فقط (ليس الإجراءات المكتملة)
        (caseItem.actions || []).forEach((action, index) => {
          console.log(`🔍 فحص إجراء ${index} في القضية ${caseItem.fullCaseNumber}:`, {
            description: action.description,
            deadline: action.deadline,
            reminderType: action.reminderType,
            isDeleted: action.isDeleted,
            isCompleted: action.isCompleted,
            hasDeadline: !!action.deadline,
            fullAction: action
          });

          if (action.isDeleted || !action.deadline || action.isCompleted) {
            console.log(`⏭️ تخطي الإجراء ${index}: محذوف أو مكتمل أو بدون موعد`);
            return;
          }
           try {
            const deadlineDate = new Date(action.deadline);
            console.log(`📅 تاريخ الموعد النهائي للإجراء ${index}:`, {
              originalDeadline: action.deadline,
              parsedDeadline: deadlineDate,
              deadlineString: deadlineDate.toDateString(),
              isValidDate: !isNaN(deadlineDate.getTime())
            });

            const shouldShow = shouldShowNotification(action, deadlineDate);
            console.log(`✅ نتيجة فحص العرض للإجراء ${index}:`, shouldShow);

            if (shouldShow) {
              // تحديد الأولوية حسب نوع التذكير والتوقيت
              let priority = PRIORITIES.MEDIUM;
              let notificationType = NOTIFICATION_TYPES.UPCOMING_ACTION;

              // إذا كان التذكير "قبل يوم واحد" أو كان اليوم هو غداً، اجعل الأولوية عالية
              if (action.reminderType === 'dayBefore' || isTomorrow(deadlineDate)) {
                priority = PRIORITIES.HIGH;
                notificationType = NOTIFICATION_TYPES.ACTION;
              }
              // إذا كان التذكير "يومي" واليوم هو يوم الموعد، اجعل الأولوية عالية
              else if (action.reminderType === 'daily') {
                const today = new Date();
                const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
                const deadlineOnly = new Date(deadlineDate.getFullYear(), deadlineDate.getMonth(), deadlineDate.getDate());

                if (todayOnly.getTime() === deadlineOnly.getTime()) {
                  priority = PRIORITIES.HIGH;
                  notificationType = NOTIFICATION_TYPES.ACTION;
                }
              }

              const notification = {
                id: `${caseItem.id}-action-${index}-${priority}`,
                type: notificationType,
                priority: priority,
                caseNumber: caseItem.fullCaseNumber || 'غير محدد',
                clientName: caseItem.clientName || 'غير محدد',
                courtLocation: caseItem.courtLocation || 'غير محددة',
                date: deadlineDate, // Store Date object
                displayDate: formatDate(action.deadline),
                description: action.description || 'لا يوجد وصف',
                reminderType: action.reminderType || 'dayBefore',
                accountType: activeAccount, // إضافة نوع الحساب للإشعار
                actionId: action.id, // إضافة معرف الإجراء
                caseId: caseItem.id, // إضافة معرف القضية
              };
              console.log(`🔔 إضافة إشعار إجراء:`, notification);
              generatedNotifications.push(notification);
            }
          } catch (e) {
            console.error(`❌ Invalid date for action ${index} in case ${caseItem.id}:`, action.deadline, e);
          }
        });
      });

      console.log('📊 إجمالي الإشعارات المُنشأة:', generatedNotifications.length);
      console.log('📋 تفاصيل الإشعارات:', generatedNotifications);

      // تشخيص مفصل للإشعارات حسب النوع
      const deferralNotifications = generatedNotifications.filter(n => n.type === NOTIFICATION_TYPES.DEFERRAL);
      const actionNotifications = generatedNotifications.filter(n => n.type === NOTIFICATION_TYPES.ACTION || n.type === NOTIFICATION_TYPES.UPCOMING_ACTION);

      console.log('🔔 إشعارات التأجيلات:', deferralNotifications.length, deferralNotifications);
      console.log('📋 إشعارات الإجراءات:', actionNotifications.length, actionNotifications);

      // تشخيص إضافي للتاريخ الحالي
      const currentDate = new Date();
      console.log('📅 التاريخ الحالي:', {
        currentDate: currentDate.toDateString(),
        currentDateISO: currentDate.toISOString(),
        currentTime: currentDate.getTime(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: currentDate.getTimezoneOffset()
      });

      // فحص إضافي للبيانات المجلبة
      console.log('🔍 فحص شامل للبيانات المجلبة:', {
        totalCases: userCases.length,
        casesWithDeferrals: userCases.filter(c => c.deferrals && c.deferrals.length > 0).length,
        casesWithActions: userCases.filter(c => c.actions && c.actions.length > 0).length,
        totalDeferrals: userCases.reduce((sum, c) => sum + (c.deferrals?.length || 0), 0),
        totalActions: userCases.reduce((sum, c) => sum + (c.actions?.length || 0), 0),
        activeDeferrals: userCases.reduce((sum, c) => sum + (c.deferrals?.filter(d => !d.isDeleted && d.date).length || 0), 0),
        activeActions: userCases.reduce((sum, c) => sum + (c.actions?.filter(a => !a.isDeleted && !a.isCompleted && a.deadline).length || 0), 0)
      });

      // Sort by priority (High first), then by date (soonest first)
      generatedNotifications.sort((a, b) => {
        const priorityOrder = { [PRIORITIES.HIGH]: 1, [PRIORITIES.MEDIUM]: 2 };
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[a.priority] - priorityOrder[b.priority];
        }
        // If priorities are the same, sort by date (ascending)
        return a.date - b.date;
      });

      // إضافة مصدر البيانات لكل إشعار
      generatedNotifications.forEach(notification => {
        notification.accountType = activeAccount;
      });

      console.log('تم تعيين الإشعارات:', generatedNotifications.length);
      setAllNotifications(generatedNotifications);
      applyFilter(activeFilter, generatedNotifications); // Apply current filter to new data

      // 3. Update cache and local storage
      try {
        // تحديث التخزين المؤقت مع إضافة مصدر البيانات
        localStorage.setItem(cacheKey, JSON.stringify({
          data: generatedNotifications,
          timestamp: Date.now(),
          source: activeAccount // حفظ مصدر البيانات
        }));

        // حفظ الإشعارات في التخزين المحلي إذا كان الحساب محلي
        if (activeAccount === 'local') {
          saveLocalNotifications(userId, generatedNotifications);
        }
      } catch (e) {
        console.error("Error writing to cache:", e);
      }

    } catch (e) {
      console.error('Error fetching notifications:', e);

      // رسالة خطأ مختلفة حسب الحساب النشط
      if (activeAccount === 'online') {
        setError('حدث خطأ أثناء جلب الإشعارات من السيرفر. يرجى المحاولة لاحقًا أو التحقق من اتصالك.');
      } else {
        setError('حدث خطأ أثناء جلب الإشعارات المحلية.');
      }

      setAllNotifications([]); // Clear potentially stale data on error
      setFilteredNotifications([]);
    } finally {
      setLoading(false);
    }
  }, [currentUser, cacheKey, activeFilter]); // Include activeFilter to re-apply filter when data re-fetches

  // --- Filtering Logic ---
  const applyFilter = useCallback((filterType, notificationsList) => {
    if (filterType === 'all') {
      setFilteredNotifications(notificationsList);
    } else {
      setFilteredNotifications(notificationsList.filter(n => n.priority === filterType));
    }
  }, []); // Empty dependency array - function definition doesn't change

  // --- Filter Change Handler ---
  const handleFilterChange = (filterType) => {
    setActiveFilter(filterType);
    applyFilter(filterType, allNotifications); // Apply filter immediately to existing data
  };

  // --- Effect for Initial Fetch and Interval ---
  useEffect(() => {
    fetchNotifications(); // Initial fetch

    // إضافة مستمع للتحديثات الفورية
    const refreshListener = () => {
      console.log("Cache update detected, refreshing notifications...");
      fetchNotifications(true);
    };

    cacheManager.addListener('notifications_refresh', refreshListener);

    // Set up interval for periodic refresh (bypassing cache)
    const intervalId = setInterval(() => {
       console.log("Refreshing notifications..."); // For debugging
       fetchNotifications(true); // Force refresh
    }, 15 * 1000); // Refresh every 15 seconds for real-time updates

    // Cleanup interval and listeners on component unmount or when currentUser changes
    return () => {
      clearInterval(intervalId);
      cacheManager.removeListener('notifications_refresh', refreshListener);
    };

  }, [fetchNotifications]); // Depend on the memoized fetchNotifications function


  // --- Icon Mapping ---
  const notificationIcons = {
    [NOTIFICATION_TYPES.DEFERRAL]: FaCalendarAlt,
    [NOTIFICATION_TYPES.ACTION]: FaFileSignature,
    [NOTIFICATION_TYPES.UPCOMING_ACTION]: FaExclamationTriangle,
  };

  // --- Render Logic ---
  const renderContent = () => {
    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>جاري تحميل الإشعارات...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className={styles.errorMessage}>
          <FaExclamationTriangle className={styles.errorIcon} />
          <p>{error}</p>
        </div>
      );
    }

    if (filteredNotifications.length === 0) {
      const message = activeFilter === 'all'
        ? "لا يوجد لديك أي إشعارات حالياً"
        : `لا توجد إشعارات ذات أولوية "${activeFilter === 'high' ? 'عالية' : 'متوسطة'}"`;

      return (
        <div className={styles.emptyState}>
          <div className={styles.emptyFace}>
            <FaRegMehBlank className={styles.emptyIcon} />
          </div>
          <h3>لا توجد إشعارات للعرض</h3>
          <p>{message}</p>
        </div>
      );
    }

    // Render the list of notifications
    return (
      <div className={styles.notificationsList}>
        {filteredNotifications.map((notification) => {
          const IconComponent = notificationIcons[notification.type] || FaBell;

          return (
            <div
              key={notification.id}
              className={`${styles.notificationCard} ${styles[notification.priority]}`}
            >
              <div className={styles.cardHeader}>
                <div className={styles.notificationMeta}>
                  <IconComponent className={styles.typeIcon} />
                  <span className={styles.notificationType}>{notification.type}</span>
                  <span className={`${styles.accountBadge} ${notification.accountType === 'online' ? styles.onlineBadge : styles.localBadge}`}>
                    {notification.accountType === 'online' ? 'متصل' : 'محلي'}
                  </span>
                </div>
                <span className={styles.date}>{notification.displayDate}</span>
              </div>

              <div className={styles.cardBody}>
                <div className={styles.caseInfo}>
                  <h3 className={styles.caseNumber}>
                    <FaFileAlt className={styles.caseIcon} />
                    {notification.caseNumber}
                  </h3>
                  <div className={styles.clientInfo}>
                    <span className={styles.infoLabel}>الموكل:</span>
                    <span className={styles.infoValue}>{notification.clientName}</span>
                  </div>
                  <div className={styles.courtInfo}>
                    <span className={styles.infoLabel}>المحكمة:</span>
                    <span className={styles.infoValue}>{notification.courtLocation}</span>
                  </div>
                </div>

                <div className={styles.notificationDetails}>
                  {notification.type === NOTIFICATION_TYPES.DEFERRAL && (
                    <div className={styles.detailItem}>
                      <span className={styles.detailLabel}>سبب التأجيل:</span>
                      <span className={styles.detailValue}>{notification.reasons}</span>
                    </div>
                  )}

                  {(notification.type === NOTIFICATION_TYPES.ACTION || notification.type === NOTIFICATION_TYPES.UPCOMING_ACTION) && (
                    <>
                      <div className={styles.detailItem}>
                        <span className={styles.detailLabel}>تفاصيل الإجراء:</span>
                        <span className={styles.detailValue}>{notification.description}</span>
                      </div>
                      {notification.reminderType && (
                        <div className={styles.detailItem}>
                          <span className={styles.detailLabel}>نوع التذكير:</span>
                          <span className={styles.detailValue}>
                            {notification.reminderType === 'daily' ? 'إشعار يومي' : 'إشعار قبلها بيوم'}
                          </span>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* أزرار الإجراءات */}
                <div className={styles.notificationActions}>
                  {(notification.type === NOTIFICATION_TYPES.ACTION || notification.type === NOTIFICATION_TYPES.UPCOMING_ACTION) && (
                    <button
                      className={styles.completeButton}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleActionComplete(notification);
                      }}
                      title="تم تنفيذ التنبيه"
                    >
                      <FaCheck className={styles.buttonIcon} />
                      <span>تم التنفيذ</span>
                    </button>
                  )}

                  {notification.type === NOTIFICATION_TYPES.DEFERRAL && (
                    <button
                      className={styles.completeButton}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeferralComplete(notification);
                      }}
                      title="تم حضور الجلسة"
                    >
                      <FaCheck className={styles.buttonIcon} />
                      <span>تم الحضور</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={styles.pageContainer}>
      {/* Ensure TopBar passes currentUser if needed */}
      <TopBar currentUser={currentUser} />

      <div className={styles.contentWrapper}>
        <div className={styles.mainPanel}>
          <div className={styles.header}>
            <div className={styles.headerTitle}>
              <FaBell className={styles.headerIcon} />
              <h1 className={styles.pageTitle}>الإشعارات</h1>
            </div>
            <div className={styles.accountStatus}>
              {activeAccount === 'online' ? (
                <span className={styles.onlineStatus}>
                  <FaGlobe className={styles.statusIcon} />
                  حساب متصل
                </span>
              ) : (
                <span className={styles.localStatus}>
                  <FaMobileAlt className={styles.statusIcon} />
                  حساب محلي
                </span>
              )}
            </div>
          </div>

          <div className={styles.filterTabs}>
            <button
              className={`${styles.filterTab} ${activeFilter === 'all' ? styles.activeTab : ''}`}
              onClick={() => handleFilterChange('all')}
              disabled={loading}
            >
              <span>جميع الإشعارات</span>
              <span className={styles.tabCount}>{allNotifications.length}</span>
            </button>
            <button
              className={`${styles.filterTab} ${activeFilter === 'high' ? styles.activeTab : ''}`}
              onClick={() => handleFilterChange('high')}
              disabled={loading}
            >
              <span>أولوية عالية</span>
              <span className={styles.tabCount}>{allNotifications.filter(n => n.priority === 'high').length}</span>
            </button>
            <button
              className={`${styles.filterTab} ${activeFilter === 'medium' ? styles.activeTab : ''}`}
              onClick={() => handleFilterChange('medium')}
              disabled={loading}
            >
              <span>أولوية متوسطة</span>
              <span className={styles.tabCount}>{allNotifications.filter(n => n.priority === 'medium').length}</span>
            </button>
          </div>

          <div className={styles.notificationsContainer}>
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage;