/* حاوية الصفحة الرئيسية */
.pageWrapper {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 0px;
}

/* الحاوية الرئيسية للمحتوى - نفس تصميم الكروت */
.combinedPane {
  background: #ffffff;
  border-radius: 15px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  /* إزالة الإطار الأزرق */
  border: none;
  padding: 0;
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  position: relative;
}

/* حاوية سجل المهام مع نفس تصميم الكروت */
.historyContainer {
  background: #ffffff;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  padding: 0;
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  position: relative;
  box-shadow: 0 1px 15px rgba(26, 32, 44, 0.218);
}

/* المحتوى الداخلي */
.combinedContainer {
  padding: 24px;
  margin-top: 4px;
}

.container {
  max-width: 900px;
  margin: 0 auto;
  padding-top: 20px;
}

/* Header Styles - البار العلوي الملتصق */
.historyHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  padding: 16px 20px;
  background: linear-gradient(135deg, #014871 0%, #4a8fa3 50%, #d7ede2 100%);
  border-radius: 15px 15px 0 0;
  border: none;
  position: relative;
  z-index: 1;
}

.headerIcon {
  color: white;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.headerTitle {
  font-size: 1.5rem;
  color: white;
  margin: 0;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* المحتوى الداخلي لسجل المهام */
.historyContent {
  padding: 0;
  background: linear-gradient(135deg, rgba(1, 72, 113, 0.08) 0%, rgba(74, 143, 163, 0.12) 50%, rgba(215, 237, 226, 0.15) 100%);
  border-radius: 0 0 15px 15px;
}

/* Items List */
.itemsList {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* عناصر المهام - ملتصقة ببعضها */
.historyItem,
.combinedItem {
  background: transparent;
  border-radius: 0;
  padding: 16px 20px;
  box-shadow: none;
  transition: all 0.2s ease;
  margin: 0;
  border: none;
  border-bottom: 2px dashed rgba(1, 72, 113, 0.35);
  position: relative;
}

/* أول عنصر ملتصق بالبار */
.historyItem:first-child,
.combinedItem:first-child {
  border-top: 2px dashed rgba(1, 72, 113, 0.35);
}

/* آخر عنصر بدون خط سفلي */
.historyItem:last-child,
.combinedItem:last-child {
  border-bottom: none;
  border-radius: 0 0 15px 15px;
}

.historyItem:hover,
.combinedItem:hover {
  background: rgba(1, 72, 113, 0.15);
  transform: none;
  box-shadow: none;
}

/* المحتوى الداخلي للعناصر */
.itemContent,
.reportContent,
.actionContent {
  padding: 0;
  margin: 0;
}

/* Item Content - التاريخ أولاً ثم البيانات بجانبه */
.itemContent {
  display: flex;
  align-items: center;
  gap: 16px;
}

.itemDate {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: #014871;
  background: rgba(1, 72, 113, 0.18);
  padding: 8px 12px;
  border-radius: 20px;
  min-width: fit-content;
  font-weight: 700;
  border: 1px solid rgba(1, 72, 113, 0.4);
  box-shadow: 0 1px 3px rgba(1, 72, 113, 0.2);
}

.itemMain {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.itemText {
  font-size: 1rem;
  color: #1a1a1a;
  line-height: 1.5;
  font-weight: 600;
  flex: 1;
}

.dateIcon {
  font-size: 14px;
  color: #014871;
  font-weight: bold;
}

/* Actions */
.itemActions {
  display: flex;
  gap: 8px;
}

/* الأزرار - تصميم بسيط ونظيف */
.completeButton,
.deleteButton {
  width: 44px;
  height: 44px;
  padding: 0; /* تأكد من عدم وجود padding */
  border: none;
  border-radius: 50%;
  background: var(--neutral-100);
  color: var(--neutral-600);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  flex-shrink: 0;
  box-sizing: border-box; /* مهم جداً */
}

.completeButton {
  color: #00b894;
  border: 1px solid #00b894;
}

.completeButton:hover {
  background: #00b894;
  color: white;
}

.deleteButton {
  color: #d63031;
  border: 1px solid #d63031;
}

.deleteButton:hover {
  background: #d63031;
  color: white;
}

/* Link Indicator */
.linkIndicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #0984e3;
  background: #e3f2fd;
  padding: 4px 8px;
  border-radius: 12px;
  margin-right: 8px;
}

.linkIcon {
  font-size: 12px;
}

/* حالة فارغة - تصميم موحد مع سجل المهام */
.noReports {
  text-align: center;
  padding: 16px 20px; /* نفس padding المستخدم في historyItem */
  background: linear-gradient(135deg, rgba(1, 72, 113, 0.08) 0%, rgba(74, 143, 163, 0.12) 50%, rgba(215, 237, 226, 0.15) 100%); /* نفس خلفية historyContent */


  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px; /* تصغير المسافة بين الأيقونة والنص لتتناسب مع التصميم العام */
  color: #1a1a1a; /* نفس لون النص في itemText */
  font-size: 1rem; /* نفس حجم الخط في itemText */
  font-weight: 600; /* نفس وزن الخط في itemText */
  line-height: 1.5; /* نفس line-height في itemText */
  position: relative;
  overflow: hidden;

}

.noReports::before {
  content: '📋';
  font-size: 2rem; /* تصغير الأيقونة لتتناسب مع التصميم العام */
  display: block;
  opacity: 0.6; /* تقليل الشفافية لتتناسب مع الأيقونات الأخرى */
  filter: none; /* إزالة الفلتر لتبسيط المظهر */
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 40px 20px;
  border-radius: 12px;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.emptyTitle {
  font-size: 1.2rem;
  color: #2d3436;
  margin-bottom: 8px;
  font-weight: 600;
}

.emptyText {
  font-size: 0.95rem;
  color: #636e72;
  margin: 0;
}

/* Loading State */
.loadingState {
  text-align: center;
  padding: 40px 20px;
}

.loadingSpinner {
  border: 3px solid #f5f6fa;
  border-top: 3px solid #6c5ce7;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loadingText {
  font-size: 1rem;
  color: #636e72;
}

/* Action Button Styles */
.actionButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  border-radius: 20px;
  background: rgba(1, 72, 113, 0.1); /* لون خلفية خفيف */
  color: #014871; /* لون الخط الرئيسي */
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.actionButton:hover {
  background: rgba(1, 72, 113, 0.2); /* لون خلفية أغمق عند التحويم */
}

.actionButton .buttonIcon {
  font-size: 1rem;
}