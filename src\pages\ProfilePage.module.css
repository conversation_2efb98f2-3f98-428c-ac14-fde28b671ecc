:root {
  /* لوحة الألوان الحمراء الإضافية */
  --red-primary: #622872; /* بنفسجي داكن */
  --red-secondary: #caa5cb; /* وردي فاتح */
  --red-light: #e8ddea; /* وردي باهت */
  --red-lightest: #faeaf6; /* وردي فاتح جداً */

  /* لوحة الألوان الزرقاء الإضافية */
  --blue-darkest: #00033a; /* أزرق داكن جداً */
  --blue-dark-alt: #162647; /* أزرق داكن بديل */
  --blue-medium-alt: #163473; /* أزرق متوسط بديل */
  --accent-gold: #d2ab17; /* ذهبي */
}

.pageWrapper {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  color: #fff;
  overflow: hidden; /* Prevent scroll on the entire page */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.mainContainer {
  flex: 1; /* Take remaining height */
  display: flex; /* Use flexbox for sidebar and main content */
  padding: 15px;
  gap: 15px;
  overflow: hidden; /* Prevent scroll within the main container */
  height: calc(100vh - 70px); /* Adjust height considering TopBar */
}

/* Account Management Section (Left Sidebar) */
.accountManagementSection {
  width: 300px; /* Fixed width for the sidebar */
  flex-shrink: 0; /* Prevent sidebar from shrinking */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: hidden; /* Prevent scroll within the sidebar itself */
  height: 100%; /* Fill the height of mainContainer */
}

/* Main Content Area (Right) */
.mainContentArea {
  flex: 1; /* Take remaining width */
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: hidden; /* Prevent scroll within the main content area */
  height: 100%; /* Fill the height of mainContainer */
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px; /* Reduced padding */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  flex-shrink: 0; /* Prevent header from shrinking */
  height: auto; /* Auto height based on content */
}

.pageTitle {
  color: #fff;
  margin: 0;
  font-size: 1.6rem; /* Slightly smaller title */
  font-weight: 600;
}

/* Personal Info Section */
.personalInfoSection {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1; /* Allow this section to grow and shrink */
  overflow: hidden; /* Prevent internal scroll */
}

.profileDetails {
  display: flex;
  flex-direction: column;
  gap: 8px; /* Reduced gap */
  overflow: hidden; /* Ensure no scroll here */
}

.profileField {
  display: grid;
  grid-template-columns: auto 1fr; /* Label takes auto width, input takes rest */
  gap: 10px;
  align-items: center;
  padding: 8px 10px; /* Reduced padding */
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 38px; /* Reduced height */
}

.profileField label {
  color: #eee;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem; /* Slightly smaller font */
  white-space: nowrap;
}

.fieldIcon {
  color: #a5b4fc; /* Lighter icon color */
  font-size: 0.9rem;
}

.profileField span,
.profileField input {
  color: #fff;
  text-align: right;
  word-break: break-word;
  font-size: 0.85rem;
  background: transparent; /* Remove inner background */
  padding: 6px 8px;
  border-radius: 4px;
  border: none; /* Remove border for span */
  width: 100%; /* Ensure input/span takes full width */
}

.profileField input {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.profileField input:focus {
  outline: none;
  border-color: #a5b4fc;
  background: rgba(0, 0, 0, 0.3);
}

/* Buttons */
.buttonRow {
  display: flex;
  gap: 10px;
  justify-content: flex-end; /* Align buttons to the right */
  padding-top: 10px;
  flex-shrink: 0; /* Prevent button row from shrinking */
}

.editButton,
.saveButton,
.cancelButton {
  padding: 8px 20px; /* Reduced padding */
  border: none;
  border-radius: 18px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  height: 36px; /* Reduced height */
}

.editButton {
  background: linear-gradient(135deg, #74c0fc, #4dabf7);
  color: #fff;
}
.editButton:hover { background: linear-gradient(135deg, #4dabf7, #339af0); transform: translateY(-1px); }

.saveButton {
  background: linear-gradient(135deg, #51cf66, #40c057);
  color: #fff;
}
.saveButton:hover { background: linear-gradient(135deg, #40c057, #37b24d); transform: translateY(-1px); }

.cancelButton {
  background: linear-gradient(135deg, #ff6b6b, #fa5252);
  color: #fff;
}
.cancelButton:hover { background: linear-gradient(135deg, #fa5252, #e03131); transform: translateY(-1px); }

/* Account Management Specific Styles */
.accountButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
}
.accountButton:hover { background: rgba(255, 255, 255, 0.2); }
.accountButton.activeOnline { background: #3498db; border-color: #3498db; }
.accountButton.activeLocal { background: #2ecc71; border-color: #2ecc71; }

.accountIcon { font-size: 1.1rem; }

.accountStatus {
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space out elements */
  gap: 8px;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.15);
  padding: 8px 12px;
  border-radius: 6px;
}
.accountStatus span { display: flex; align-items: center; gap: 5px; }
.onlineText { color: #3498db; }
.localText { color: #2ecc71; }

.statusOnline, .statusOffline {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.statusOnline { background-color: #4CAF50; box-shadow: 0 0 6px #4CAF50; }
.statusOffline { background-color: #f44336; box-shadow: 0 0 6px #f44336; }

.accountInfoBox {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.accountInfoField {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
}
.accountInfoField label { font-weight: 500; color: #ccc; }
.accountInfoField span { color: #fff; }

.accountActions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: auto; /* Push actions to the bottom */
}

.manageMembersButton,
.viewOfflineButton {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}
.manageMembersButton:hover { background: rgba(255, 255, 255, 0.15); }
.viewOfflineButton { background: rgba(255, 193, 7, 0.15); border-color: rgba(255, 193, 7, 0.3); }
.viewOfflineButton:hover { background: rgba(255, 193, 7, 0.25); }

/* Section Titles */
.sectionTitle {
  color: #fff;
  margin: 0 0 10px 0;
  font-size: 1.1rem; /* Reduced size */
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding-bottom: 6px;
}

.sectionIcon { font-size: 1.2rem; }
/* Adjust icon colors for sections */
.accountManagementSection .sectionIcon { color: #81c784; } /* Greenish for account */
.personalInfoSection .sectionIcon { color: #a5b4fc; } /* Lighter blue for personal */

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  position: absolute; /* Center in the pageWrapper */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 30px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  z-index: 10;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-left: 4px solid #fff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

.errorContainer { color: #ff8a80; background: rgba(255, 138, 128, 0.2); border: 1px solid rgba(255, 138, 128, 0.4); }
.errorMessage { color: #ff8a80; background: rgba(255, 138, 128, 0.1); padding: 10px; border-radius: 6px; margin-bottom: 10px; border: 1px solid rgba(255, 138, 128, 0.3); text-align: center; font-size: 0.9rem; }

/* Modal Styles */
.modalOverlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.7); display: flex; justify-content: center; align-items: center; z-index: 1000; backdrop-filter: blur(5px); }
.modal { background: rgba(40, 40, 40, 0.9); border-radius: 12px; padding: 25px; max-width: 450px; width: 90%; text-align: center; box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4); border: 1px solid rgba(255, 255, 255, 0.1); }
.modal h3 { margin-top: 0; margin-bottom: 15px; color: #fff; font-size: 1.3rem; }
.modal p { margin-bottom: 20px; line-height: 1.5; font-size: 0.95rem; color: #eee; }
.modalButtons { display: flex; gap: 15px; margin-top: 20px; justify-content: center; }
.confirmButton { background: #3b82f6; color: #fff; padding: 10px 20px; border-radius: 8px; font-size: 0.95rem; cursor: pointer; transition: all 0.2s ease; border: none; }
.confirmButton:hover { background: #2563eb; }
/* Use the same cancelButton style */

/* Responsive Design */
@media (max-width: 768px) {
  .mainContainer {
    flex-direction: column; /* Stack sidebar and content vertically */
    height: auto; /* Allow container to grow */
    padding: 10px;
    overflow-y: auto; /* Allow scroll on the container for small screens */
    overflow-x: hidden;
  }

  .accountManagementSection {
    width: 100%; /* Full width */
    height: auto; /* Adjust height based on content */
    margin-bottom: 15px; /* Add space between sections */
  }

  .mainContentArea {
    width: 100%;
    height: auto; /* Adjust height based on content */
  }

  .pageHeader {
    padding: 10px 15px;
  }

  .pageTitle {
    font-size: 1.4rem;
  }

  .profileField {
    grid-template-columns: 1fr; /* Stack label and input */
    gap: 5px;
    padding: 10px;
  }

  .profileField label {
    justify-content: flex-start; /* Align label left */
    font-size: 0.8rem;
  }

  .profileField span,
  .profileField input {
    text-align: left; /* Align text left */
    font-size: 0.85rem;
  }

  .buttonRow {
    justify-content: center; /* Center buttons */
  }
}

