/* General styles */
body {
  background-color: #f8fafc;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: 'Inter', 'Cairo', sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 15px;
  display: flex;
  justify-content: center;
}

/* Case Registration Container */
.case-registration-container {
  max-width: 800px;
  width: 100%;
  margin: 10px auto;
  padding: 20px;
  border-radius: 6px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

/* Form Title */
.form-title,
.case-registration-container h2 {
  text-align: center;
  margin-bottom: 20px;
  font-size: 1.4rem;
  color: #1e293b;
  font-weight: 600;
  position: relative;
  padding: 10px 0;
  background-color: #f1f5f9;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Transfer Info */
.transfer-info {
  background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
  border: 2px solid #2196f3;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.transfer-info p {
  margin: 0;
  color: #1976d2;
  font-weight: 500;
  font-size: 1rem;
}

.transfer-info strong {
  color: #0d47a1;
  font-weight: 700;
}

/* Form structure */
.case-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Form rows */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  flex-wrap: wrap;
  padding: 12px;
  border-radius: 6px;
  align-items: flex-start;
  background-color: #f8fafc;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.empty-group {
  min-height: 10px;
}

.helper-text {
  color: #64748b;
  font-size: 0.85rem;
  margin-top: 8px;
  display: block;
  width: 100%;
  background-color: #f8fafc;
  padding: 6px 10px;
  border-radius: 6px;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Button Group */
.button-group {
  display: flex;
  flex-direction: row;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 15px;
  border-top: 1px solid #e2e8f0;
  background-color: #f8fafc;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Input Group (label + input) */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  min-width: 250px;
  position: relative;
  margin-bottom: 5px;
}

.input-group label {
  font-size: 0.95rem;
  color: #334155;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 3px;
  width: 100%;
  text-align: right;
}

.required-star {
  color: #ef4444;
  font-size: 0.85rem;
}

/* Full width input group */
.input-group-full {
  width: 100%;
  flex-basis: 100%;
}

.input-group-full label {
  width: auto;
  min-width: 80px;
}

/* Input and Select fields - Base styles */
.input-group input,
.input-group select {
  padding: 10px 12px;
  border-width: 1.5px;
  border-style: solid;
  border-radius: 6px;
  font-size: 1rem;
  color: #1e293b;
  background: #ffffff;
  width: 100%;
  height: 42px;
  transition: all 0.2s;
  box-sizing: border-box;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Focus states */
.input-group input:focus,
.input-group select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Text inputs specific styles */
.input-group input[type="text"] {
  border-color: #3b82f6;
}

.input-group input[type="date"] {
  border-color: #8b5cf6;
}

.input-group input[type="number"] {
  border-color: #f59e0b;
}

/* Select inputs specific styles */
.input-group select,
.select-field {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 10px center;
  padding-left: 30px;
  border-color: #10b981;
  min-width: 180px;
}

/* Case Number Inputs Specific Layout */
.case-number-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  margin-top: 5px;
}

.case-number-input,
.case-year-input {
  width: 100px;
  text-align: center;
  padding: 10px 5px;
  border-width: 1.5px;
  border-style: solid;
  border-color: #f59e0b;
  border-radius: 6px;
  background-color: white;
  font-size: 1rem;
  height: 42px;
  box-sizing: border-box;
  min-width: 100px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.separator {
  color: #475569;
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0 5px;
}

.generated-number {
  margin-top: 10px;
  font-size: 0.9rem;
  color: #475569;
  display: block;
  background-color: #f1f5f9;
  padding: 8px 12px;
  border-radius: 6px;
  width: 100%;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Button styles */
.button-group button {
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  padding: 8px 16px;
  min-width: 100px;
  transition: all 0.2s;
  height: 40px;
}

/* Save Button */
.case-save-btn {
  background-color: #3b82f6;
  color: white;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.case-save-btn:hover {
  background-color: #2563eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Cancel Button */
.case-cancel-btn {
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.case-cancel-btn:hover {
  background-color: #e2e8f0;
  color: #334155;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .case-registration-container {
    padding: 15px;
    width: 95%;
    margin: 10px auto;
  }

  .form-title,
  .case-registration-container h2 {
    font-size: 1.2rem;
    padding: 8px 0;
    margin-bottom: 15px;
  }

  .form-row {
    gap: 0;
    padding: 10px;
    margin-bottom: 10px;
    flex-direction: column;
  }

  .input-group {
    width: 100%;
    margin-bottom: 15px;
    min-width: 100%;
  }

  .input-group:last-child {
    margin-bottom: 0;
  }

  .input-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
  }

  .input-group input,
  .input-group select {
    width: 100%;
    font-size: 0.95rem;
    height: 40px;
  }

  .helper-text {
    width: 100%;
    font-size: 0.8rem;
    padding: 5px 8px;
  }

  .generated-number {
    width: 100%;
    font-size: 0.85rem;
    padding: 6px 10px;
  }

  .case-number-inputs {
    justify-content: center;
    gap: 10px;
  }

  .case-number-input,
  .case-year-input {
    width: 90px;
    min-width: 90px;
    height: 40px;
  }

  .button-group {
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
    padding: 15px;
  }

  .case-save-btn,
  .case-cancel-btn {
    width: 100%;
    font-size: 0.95rem;
    padding: 10px;
    height: 45px;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .case-registration-container {
    padding: 10px;
  }

  .form-title,
  .case-registration-container h2 {
    font-size: 1.1rem;
    padding: 6px 0;
  }

  .form-row {
    padding: 8px;
    margin-bottom: 8px;
  }

  .input-group {
    margin-bottom: 12px;
  }

  .input-group label {
    font-size: 0.85rem;
  }

  .input-group input,
  .input-group select {
    font-size: 0.9rem;
    padding: 8px;
    height: 38px;
  }

  .helper-text {
    font-size: 0.75rem;
    padding: 4px 6px;
  }

  .case-number-inputs {
    gap: 8px;
  }

  .case-number-input,
  .case-year-input {
    width: 80px;
    min-width: 80px;
    height: 38px;
    font-size: 0.9rem;
  }

  .button-group {
    padding: 10px;
  }

  .case-save-btn,
  .case-cancel-btn {
    height: 40px;
    font-size: 0.9rem;
  }
}

/* Error and Success Messages */
.error-message {
  background-color: #fef2f2;
  color: #b91c1c;
  border-right: 3px solid #ef4444;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.success-message {
  background-color: #f0fdf4;
  color: #166534;
  border-right: 3px solid #10b981;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Loading Spinner */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border-right: 3px solid #3b82f6;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

.loading-text {
  color: #475569;
  font-size: 0.9rem;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}