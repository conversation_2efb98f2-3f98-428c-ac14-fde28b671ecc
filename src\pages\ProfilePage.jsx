import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaEdit, FaSave, FaTimes, FaBuilding, FaBriefcase, FaUsers, FaGlobe, FaMobileAlt, FaUser, FaEnvelope, FaPhone, FaUserTie, FaExchangeAlt } from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import styles from './ProfilePage.module.css';
import { db } from '../config/firebaseConfig';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { getActiveAccount, setActiveAccount } from '../services/StorageService';

const ProfilePage = ({ currentUser }) => {
  const navigate = useNavigate();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    jobTitle: ''
  });
  const [companyName, setCompanyName] = useState('اسم الشركة/المجموعة الافتراضي');
  const [isAccountOwner, setIsAccountOwner] = useState(true);
  const [userRole, setUserRole] = useState('manager');
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());
  const [localUserData, setLocalUserData] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser || !currentUser.uid) {
        setError('المستخدم غير مسجل الدخول. يرجى تسجيل الدخول مرة أخرى.');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      // تحميل بيانات الحساب الأونلاين
      try {
        const userRef = doc(db, 'users', currentUser.uid);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          const data = userSnap.data();
          setUserData(data);
          if (activeAccount === 'online') {
            setFormData({
              name: data.name || '',
              email: data.email || '',
              phone: data.phone || '',
              company: data.company || '',
              jobTitle: data.jobTitle || '',
            });
          }
          setCompanyName(data.onlineAccountCompanyName || data.company || 'اسم الشركة/المجموعة الافتراضي');
          setIsAccountOwner(data.isOnlineAccountOwner || false);
          setUserRole(data.userRole || 'manager');
        } else {
          console.log('لم يتم العثور على بيانات المستخدم في Firestore.');
        }
      } catch (e) {
        console.error('خطأ في جلب بيانات المستخدم من Firestore:', e.message);
      }

      // تحميل بيانات الحساب المحلي
      try {
        const localData = localStorage.getItem('localUserData_' + currentUser.uid);
        if (localData) {
          const parsedLocalData = JSON.parse(localData);
          setLocalUserData(parsedLocalData);
          if (activeAccount === 'local') {
            setFormData({
              name: parsedLocalData.name || '',
              email: parsedLocalData.email || '',
              phone: parsedLocalData.phone || '',
              company: parsedLocalData.company || '',
              jobTitle: parsedLocalData.jobTitle || '',
            });
          }
        } else {
          console.log('لم يتم العثور على بيانات محلية للمستخدم.');
        }
      } catch (e) {
        console.error('خطأ في قراءة البيانات المحلية:', e.message);
      }

      setLoading(false);
    };

    fetchUserData();

    const savedActiveAccount = localStorage.getItem('activeAccount');
    if (savedActiveAccount) {
      setActiveAccount(savedActiveAccount);
    }

    const handleOnlineStatusChange = () => {
      if (!navigator.onLine && activeAccount === 'online') {
        alert('أنت الآن غير متصل بالإنترنت. قد تواجه مشكلات في الوصول إلى بيانات الحساب الأونلاين. يمكنك التبديل إلى الحساب المحلي للاستمرار في العمل دون اتصال.');
      }
    };
    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);
    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
    };
  }, [currentUser, activeAccount]);

  const handleEdit = () => setEditMode(true);

  const handleCancel = () => {
    setEditMode(false);
    const currentData = activeAccount === 'online' ? userData : localUserData;
    if (currentData) {
        setFormData({
            name: currentData.name || '',
            email: currentData.email || '',
            phone: currentData.phone || '',
            company: currentData.company || '',
            jobTitle: currentData.jobTitle || '',
        });
    }
    setError(null);
  };

  const handleSave = async () => {
    if (!currentUser?.uid) {
      setError('المستخدم غير مسجل الدخول.');
      return;
    }
    if (!formData.name || !formData.email) {
      setError('الاسم والبريد الإلكتروني حقلان مطلوبان.');
      return;
    }

    try {
      setEditMode(false);
      setError(null);

      if (activeAccount === 'online') {
        if (navigator.onLine) {
          const userRef = doc(db, 'users', currentUser.uid);
          await updateDoc(userRef, {
            name: formData.name,
            phone: formData.phone,
            company: formData.company,
            jobTitle: formData.jobTitle,
            lastUpdateTime: new Date().toISOString()
          });
          setUserData({ ...userData, ...formData, lastUpdateTime: new Date().toISOString() });
          alert('تم حفظ البيانات بنجاح في الحساب الأونلاين.');
        } else {
          setError('لا يمكن حفظ البيانات في الحساب الأونلاين بدون اتصال بالإنترنت.');
        }
      } else { // activeAccount === 'local'
        const updatedLocalData = {
          ...(localUserData || {}),
          uid: currentUser.uid,
          ...formData,
          lastUpdatedAt: new Date().toISOString()
        };
        localStorage.setItem('localUserData_' + currentUser.uid, JSON.stringify(updatedLocalData));
        setLocalUserData(updatedLocalData);
        alert('تم حفظ البيانات بنجاح في الحساب المحلي.');
      }
    } catch (e) {
      setError('خطأ في تحديث البيانات: ' + e.message);
      if (activeAccount === 'online' && !navigator.onLine) {
        if (window.confirm('حدث خطأ في حفظ البيانات في الحساب الأونلاين. هل ترغب في التبديل إلى الحساب المحلي؟')) {
          switchToLocalAccount();
        }
      }
    }
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const toggleActiveAccount = () => {
    if (!currentUser?.uid) return;
    const newAccountType = activeAccount === 'online' ? 'local' : 'online';

    if (newAccountType === 'local') {
      const localData = localStorage.getItem('localUserData_' + currentUser.uid);
      if (!localData) {
        setShowConfirmModal(true);
        return;
      }
      setActiveAccount(newAccountType);
      setActiveAccountState(newAccountType);
      window.location.reload(); // Reload to apply changes globally
    } else {
      setActiveAccount(newAccountType);
      setActiveAccountState(newAccountType);
      window.location.reload(); // Reload to apply changes globally
    }
  };

  const switchToOnlineAccount = () => {
    setActiveAccount('online');
    setActiveAccountState('online');
    window.location.reload();
  };

  const switchToLocalAccount = () => {
    setActiveAccount('local');
    setActiveAccountState('local');
    window.location.reload();
  };

  const createLocalAccount = () => {
    if (!currentUser?.uid) return;
    const newLocalUserData = {
      uid: currentUser.uid,
      name: '',
      email: currentUser.email || '',
      phone: '',
      company: '',
      jobTitle: '',
      cases: [],
      createdAt: new Date().toISOString(),
      lastUpdatedAt: new Date().toISOString()
    };
    localStorage.setItem('localUserData_' + currentUser.uid, JSON.stringify(newLocalUserData));
    setLocalUserData(newLocalUserData);
    setActiveAccount('local');
    setActiveAccountState('local');
    setShowConfirmModal(false);
    alert('تم إنشاء حساب محلي جديد فارغ بنجاح والتبديل إليه.');
    window.location.reload();
  };

  const cancelCreateLocalAccount = () => setShowConfirmModal(false);

  const handleManageMembers = () => alert('وظيفة إدارة أعضاء الشركة/المجموعة (تحتاج لتطوير)');

  const handleViewOfflineAccount = () => {
    try {
      const localData = localStorage.getItem('localUserData_' + currentUser.uid);
      if (!localData) {
        alert('لا توجد بيانات محلية مخزنة بعد.');
        return;
      }
      const parsedData = JSON.parse(localData);
      const lastUpdate = new Date(parsedData.lastUpdatedAt || parsedData.createdAt);
      alert(`معلومات الحساب المحلي:\n\nالاسم: ${parsedData.name || 'غير محدد'}\nالبريد الإلكتروني: ${parsedData.email || 'غير محدد'}\nالهاتف: ${parsedData.phone || 'غير محدد'}\nالشركة: ${parsedData.company || 'غير محدد'}\nالمسمى: ${parsedData.jobTitle || 'غير محدد'}\n\nعدد القضايا: ${parsedData.cases?.length || 0}\nآخر تحديث: ${lastUpdate.toLocaleString('ar-EG')}\n\nملاحظة: بيانات محلية فقط.`);

      if (window.confirm('هل ترغب في حذف الحساب المحلي؟ (لا يمكن التراجع)')) {
        if (window.confirm('تأكيد الحذف؟')) {
          localStorage.removeItem('localUserData_' + currentUser.uid);
          setLocalUserData(null);
          switchToOnlineAccount();
          alert('تم حذف الحساب المحلي.');
        }
      }
    } catch (error) {
      alert('خطأ: ' + error.message);
    }
  };

  if (loading) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} />
        <div className={styles.loadingContainer}><div className={styles.spinner}></div><p>جاري التحميل...</p></div>
      </div>
    );
  }

  if (error && !(activeAccount === 'online' ? userData : localUserData)) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} />
        <div className={styles.errorContainer}>{error || 'خطأ في تحميل البيانات.'}</div>
      </div>
    );
  }

  // Determine current data based on active account
  const currentDisplayData = activeAccount === 'online' ? userData : localUserData;

  return (
    <div className={styles.pageWrapper}>
      <TopBar currentUser={currentUser} />
      <div className={styles.mainContainer}> {/* Changed to flex row */} 

        {/* Account Management Section (Left Sidebar) */} 
        <div className={styles.accountManagementSection}> 
          <h3 className={styles.sectionTitle}><FaUsers className={styles.sectionIcon} /> إدارة الحساب</h3>
          
          {/* Account Toggle Button */} 
          <button 
            onClick={toggleActiveAccount} 
            className={`${styles.accountButton} ${activeAccount === 'online' ? styles.activeOnline : styles.activeLocal}`} 
          >
            <FaExchangeAlt className={styles.accountIcon} />
            <span>{activeAccount === 'online' ? 'التبديل إلى المحلي' : 'التبديل إلى الأونلاين'}</span>
          </button>

          {/* Account Status Indicator */} 
          <div className={styles.accountStatus}>
            <span>الحساب النشط:</span>
            {activeAccount === 'online' ? (
              <span className={styles.onlineText}><FaGlobe /> أونلاين</span>
            ) : (
              <span className={styles.localText}><FaMobileAlt /> محلي</span>
            )}
            <div className={navigator.onLine ? styles.statusOnline : styles.statusOffline}></div>
          </div>

          {/* Company/Group Info */} 
          <div className={styles.accountInfoBox}> 
            <div className={styles.accountInfoField}>
              <FaBuilding className={styles.fieldIcon} /> 
              <label>الشركة/المجموعة:</label>
              <span>{activeAccount === 'online' ? companyName : (localUserData?.company || 'غير محددة')}</span>
            </div>
            {activeAccount === 'online' && (
              <>
                <div className={styles.accountInfoField}>
                  <FaUserTie className={styles.fieldIcon} /> 
                  <label>الدور:</label>
                  <span>{userRole === 'manager' ? 'مدير' : 'عضو'}</span>
                </div>
                <div className={styles.accountInfoField}>
                  <FaUser className={styles.fieldIcon} /> 
                  <label>الحالة:</label>
                  <span>{isAccountOwner ? 'مالك الحساب' : 'عضو'}</span>
                </div>
              </>
            )}
          </div>

          {/* Account Actions */} 
          <div className={styles.accountActions}> 
            {activeAccount === 'online' && isAccountOwner && (
              <button onClick={handleManageMembers} className={styles.manageMembersButton}>
                <FaUsers /> إدارة الأعضاء
              </button>
            )}
            {localUserData && (
              <button onClick={handleViewOfflineAccount} className={styles.viewOfflineButton}>
                <FaMobileAlt /> عرض/حذف الحساب المحلي
              </button>
            )}
          </div>
        </div>

        {/* Main Content Area (Right) */} 
        <div className={styles.mainContentArea}> 
          <div className={styles.pageHeader}>
            <h2 className={styles.pageTitle}>الملف الشخصي</h2>
            {/* Moved account toggle button to the left sidebar */} 
          </div>

          {error && <div className={styles.errorMessage}>{error}</div>}

          {/* Personal Info Section */} 
          <div className={styles.personalInfoSection}> 
            <h3 className={styles.sectionTitle}><FaUser className={styles.sectionIcon} /> المعلومات الشخصية</h3>
            <div className={styles.profileDetails}> 
              <div className={styles.profileField}>
                <label><FaUser className={styles.fieldIcon} /> الاسم:</label>
                {editMode ? (
                  <input type="text" name="name" value={formData.name} onChange={handleChange} />
                ) : (
                  <span>{currentDisplayData?.name || 'غير محدد'}</span>
                )}
              </div>
              <div className={styles.profileField}>
                <label><FaEnvelope className={styles.fieldIcon} /> البريد الإلكتروني:</label>
                {/* Email is generally not editable */} 
                <span>{currentDisplayData?.email || currentUser?.email || 'غير محدد'}</span>
              </div>
              <div className={styles.profileField}>
                <label><FaPhone className={styles.fieldIcon} /> رقم الهاتف:</label>
                {editMode ? (
                  <input type="tel" name="phone" value={formData.phone} onChange={handleChange} />
                ) : (
                  <span>{currentDisplayData?.phone || 'غير محدد'}</span>
                )}
              </div>
              <div className={styles.profileField}>
                <label><FaBuilding className={styles.fieldIcon} /> الشركة:</label>
                {editMode ? (
                  <input type="text" name="company" value={formData.company} onChange={handleChange} />
                ) : (
                  <span>{currentDisplayData?.company || 'غير محدد'}</span>
                )}
              </div>
              <div className={styles.profileField}>
                <label><FaBriefcase className={styles.fieldIcon} /> المسمى الوظيفي:</label>
                {editMode ? (
                  <input type="text" name="jobTitle" value={formData.jobTitle} onChange={handleChange} />
                ) : (
                  <span>{currentDisplayData?.jobTitle || 'غير محدد'}</span>
                )}
              </div>
            </div>
          </div>

          {/* Buttons */} 
          <div className={styles.buttonRow}> 
            {editMode ? (
              <>
                <button onClick={handleSave} className={styles.saveButton}><FaSave /> حفظ</button>
                <button onClick={handleCancel} className={styles.cancelButton}><FaTimes /> إلغاء</button>
              </>
            ) : (
              <button onClick={handleEdit} className={styles.editButton}><FaEdit /> تعديل</button>
            )}
          </div>
        </div>
      </div>

      {/* Modal for creating local account */} 
      {showConfirmModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <h3>إنشاء حساب محلي؟</h3>
            <p>لا يوجد حساب محلي مرتبط بهذا المستخدم. هل ترغب في إنشاء حساب محلي جديد فارغ؟ سيتم تخزين بياناته بشكل منفصل تماماً عن الحساب الأونلاين.</p>
            <div className={styles.modalButtons}>
              <button onClick={createLocalAccount} className={styles.confirmButton}>نعم، إنشاء حساب محلي</button>
              <button onClick={cancelCreateLocalAccount} className={styles.cancelButton}>إلغاء</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfilePage;

