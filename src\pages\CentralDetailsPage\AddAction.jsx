import { useState, useEffect } from 'react';
import { FaClock, FaTimes } from 'react-icons/fa';
import styles from "./AddDeferralAction.module.css";

const AddAction = ({  currentUser, caseItem, deferrals, actions, setActions, history, setHistory,
  onSave, onClose, templateActions = [],
}) => {
  const [newAction, setNewAction] = useState('');
  const [actionDeadline, setActionDeadline] = useState('');
  const [reminderType, setReminderType] = useState('');

  const [linkedDeferralId, setLinkedDeferralId] = useState('');
  const [linkedActionId, setLinkedActionId] = useState('');
  const [linkType, setLinkType] = useState('');
  const [error, setError] = useState(null);

  const handleLinkTypeChange = (newLinkType) => {
    setLinkType(newLinkType);
    setLinkedDeferralId('');
    setLinkedActionId('');
    setError(null);
  };

  useEffect(() => {
    if (linkType === 'deferral' && linkedDeferralId) {
      const deferral = deferrals.find(d => d.id === linkedDeferralId);
      if (deferral && deferral.date) {
        const deferralDate = new Date(deferral.date);
        deferralDate.setDate(deferralDate.getDate() - 1); // يوم واحد قبل التأجيل
        setActionDeadline(deferralDate.toISOString().split('T')[0]);
      }
    } else if (linkType === 'action' && linkedActionId) {
      const linkedAction = actions.find(a => a.id === linkedActionId);
      if (linkedAction && linkedAction.deadline) {
        const linkedActionDate = new Date(linkedAction.deadline);
        linkedActionDate.setDate(linkedActionDate.getDate() + 1);
        setActionDeadline(linkedActionDate.toISOString().split('T')[0]);
        // تحقق إضافي لو الإجراء المرتبط مرتبط بتأجيل
        if (linkedAction.linkedDeferralId) {
          const deferral = deferrals.find(d => d.id === linkedAction.linkedDeferralId);
          if (deferral && deferral.date) {
            const deferralDate = new Date(deferral.date);
            deferralDate.setDate(deferralDate.getDate() - 1);
            if (linkedActionDate >= deferralDate) {
              setError('موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط بالإجراء المتسلسل بيوم واحد.');
              setActionDeadline('');
            }
          }
        }
      }
    } else if (linkType === 'custom') {
      setActionDeadline(new Date().toISOString().split('T')[0]);
    } else if (!linkType) {
      setActionDeadline('');
    }
  }, [linkType, linkedDeferralId, linkedActionId, deferrals, actions]);

  const validateDeadline = (deadline, linkedDeferralId, linkedActionId, currentLinkType) => {
    const actionDate = new Date(deadline);

    // إذا كان نوع الربط "custom" (تاريخ مخصص)، لا نطبق أي قيود
    if (currentLinkType === 'custom') {
      setError(null);
      return true;
    }

    // تحقق لو الإجراء مرتبط بتأجيل
    if (linkedDeferralId) {
      const deferral = deferrals.find(d => d.id === linkedDeferralId);
      if (deferral && deferral.date) {
        const deferralDate = new Date(deferral.date);
        const expectedDate = new Date(deferral.date);
        expectedDate.setDate(expectedDate.getDate() - 1);
        if (actionDate.toDateString() !== expectedDate.toDateString()) {
          setError('موعد الإجراء يجب أن يكون قبل تاريخ التأجيل بيوم واحد بالضبط.');
          return false;
        }
      }
    }

    // تحقق لو الإجراء مرتبط بإجراء آخر
    if (linkedActionId) {
      const linkedAction = actions.find(a => a.id === linkedActionId);
      if (linkedAction && linkedAction.deadline) {
        const linkedActionDate = new Date(linkedAction.deadline);
        const expectedDate = new Date(linkedAction.deadline);
        expectedDate.setDate(expectedDate.getDate() + 1);
        if (actionDate <= linkedActionDate) {
          setError('موعد الإجراء يجب أن يكون بعد تاريخ الإجراء المرتبط.');
          return false;
        }
        // تحقق إضافي لو الإجراء المرتبط مرتبط بتأجيل
        if (linkedAction.linkedDeferralId) {
          const deferral = deferrals.find(d => d.id === linkedAction.linkedDeferralId);
          if (deferral && deferral.date) {
            const deferralDate = new Date(deferral.date);
            const expectedDeferralDate = new Date(deferral.date);
            expectedDeferralDate.setDate(expectedDeferralDate.getDate() - 1);
            if (actionDate >= deferralDate) {
              setError('موعد الإجراء يجب أن يكون قبل تاريخ التأجيل المرتبط بالإجراء المتسلسل بيوم واحد.');
              return false;
            }
          }
        }
      }
    }

    // تحقق عام فقط إذا كان هناك ربط بتأجيل محدد
    // لا نطبق أي قيود على التاريخ المخصص إذا لم يكن هناك ارتباط
    if (linkedDeferralId && !linkedActionId) {
      const relatedDeferral = deferrals.find(d => d.id === linkedDeferralId && !d.isDeleted);
      if (relatedDeferral && relatedDeferral.date) {
        const deferralDate = new Date(relatedDeferral.date);
        const expectedDate = new Date(relatedDeferral.date);
        expectedDate.setDate(expectedDate.getDate() - 1);
        if (actionDate > expectedDate) {
          setError('موعد الإجراء يجب أن يكون قبل تاريخ التأجيل بيوم واحد على الأقل.');
          return false;
        }
      }
    }

    setError(null);
    return true;
  };

  const availableDeferrals = (deferrals || []).map((deferral, index) => ({
    id: deferral.id || `${caseItem.id}-defer-${index}`,
    content: deferral.content || (deferral.reasons ? deferral.reasons.join('، ') : `تأجيل ${index + 1}`),
    isDeleted: deferral.isDeleted || false,
    date: deferral.date,
  })).filter((deferral) => !deferral.isDeleted);

  const availableActions = (actions || []).map((action) => ({
    id: action.id,
    description: action.description || `إجراء بدون وصف`,
    isDeleted: action.isDeleted || false,
    deadline: action.deadline,
    linkedDeferralId: action.linkedDeferralId,
  })).filter((action) => !action.isDeleted);

  return (
    <div className={styles.addActionForm}>
      <div className={styles.formHeader}>
        <h3 className={styles.formTitle}>إضافة تنبيه بإجراء جديد</h3>
        <p className={styles.formSubtitle}>قم بملء البيانات المطلوبة لإضافة تنبيه بإجراء للقضية</p>
      </div>
      {error && <div className={styles.errorAlert}>{error}</div>}
      <div className={styles.actionField}>
        <label>نوع ربط الإجراء:</label>
        <select
          value={linkType}
          onChange={(e) => handleLinkTypeChange(e.target.value)}
          className={styles.actionInput}
        >
          <option value="">اختر نوع الربط</option>
          <option value="deferral">ربط بتأجيل</option>
          <option value="action">ربط بإجراء</option>
          <option value="custom">تاريخ مخصص</option>
        </select>
        {linkType === 'deferral' && (
          <>
            <div className={styles.actionField}>
              <label>اختر التأجيل:</label>
              <select
                value={linkedDeferralId}
                onChange={(e) => setLinkedDeferralId(e.target.value)}
                className={styles.actionInput}
              >
                <option value="">اختر تأجيل</option>
                {availableDeferrals.length > 0 ? (
                  availableDeferrals.map((deferral) => (
                    <option key={deferral.id} value={deferral.id}>
                      {deferral.content}
                    </option>
                  ))
                ) : (
                  <option disabled>لا توجد تأجيلات متاحة</option>
                )}
              </select>
            </div>
            {linkedDeferralId && (
              <div className={styles.actionField}>
                <label>موعد الإجراء (محدد تلقائيًا):</label>
                <input
                  type="date"
                  value={actionDeadline}
                  className={styles.actionInput}
                  disabled
                />
              </div>
            )}
          </>
        )}
        {linkType === 'action' && (
          <>
            <div className={styles.actionField}>
              <label>اختر الإجراء:</label>
              <select
                value={linkedActionId}
                onChange={(e) => setLinkedActionId(e.target.value)}
                className={styles.actionInput}
              >
                <option value="">اختر إجراء</option>
                {availableActions.length > 0 ? (
                  availableActions.map((action) => (
                    <option key={action.id} value={action.id}>
                      {action.description}
                    </option>
                  ))
                ) : (
                  <option disabled>لا توجد إجراءات متاحة</option>
                )}
              </select>
            </div>
            {linkedActionId && (
              <div className={styles.actionField}>
                <label>موعد الإجراء (محدد تلقائيًا):</label>
                <input
                  type="date"
                  value={actionDeadline}
                  className={styles.actionInput}
                  disabled
                />
              </div>
            )}
          </>
        )}
        {linkType === 'custom' && (
          <div className={styles.actionField}>
            <label>تاريخ الإجراء المخصص:</label>
            <input
              type="date"
              value={actionDeadline}
              onChange={(e) => {
                const newDeadline = e.target.value;
                // عند اختيار تاريخ مخصص، لا نمرر أي ارتباطات
                const currentLinkedDeferralId = linkType === 'custom' ? '' : linkedDeferralId;
                const currentLinkedActionId = linkType === 'custom' ? '' : linkedActionId;
                if (validateDeadline(newDeadline, currentLinkedDeferralId, currentLinkedActionId, linkType)) {
                  setActionDeadline(newDeadline);
                }
              }}
              className={styles.actionInput}
              min={new Date().toISOString().split('T')[0]}
            />
          </div>
        )}
      </div>
      <div className={styles.actionField}>
        <label>وصف الإجراء:</label>
        <input
          type="text"
          value={newAction}
          onChange={(e) => setNewAction(e.target.value)}
          placeholder="أدخل وصف الإجراء"
          className={styles.actionInput}
        />
      </div>

      <div className={styles.actionField}>
        <label>توقيت الإشعار:</label>
        <select
          value={reminderType}
          onChange={(e) => setReminderType(e.target.value)}
          className={styles.actionInput}
        >
          <option value="">اختر توقيت الإشعار</option>
          <option value="daily">إشعار يومي</option>
          <option value="dayBefore">إشعار قبلها بيوم</option>
        </select>
      </div>

      <div className={styles.actionFormButtons}>
        <button
          onClick={() => {
            if (!newAction || !actionDeadline || !reminderType) {
              setError('يرجى ملء جميع الحقول المطلوبة.');
              return;
            }
            // عند اختيار تاريخ مخصص، لا نمرر أي ارتباطات للتحقق
            const currentLinkedDeferralId = linkType === 'custom' ? '' : linkedDeferralId;
            const currentLinkedActionId = linkType === 'custom' ? '' : linkedActionId;
            if (validateDeadline(actionDeadline, currentLinkedDeferralId, currentLinkedActionId, linkType)) {
              onSave(newAction, actionDeadline, linkType, linkedDeferralId, linkedActionId, reminderType, setError);
            }
          }}
          className={styles.addActionButton}
          disabled={!!error}
        >
          <FaClock className={styles.buttonIcon} />
          <span>إضافة تنبيه بإجراء</span>
        </button>
        <button          onClick={() => {
            onClose();
            setError(null);
          }}
          className={styles.cancelButton}
        >
          <FaTimes className={styles.buttonIcon} />
          <span>إلغاء</span>
        </button>
      </div>
    </div>
  );
};

export default AddAction;