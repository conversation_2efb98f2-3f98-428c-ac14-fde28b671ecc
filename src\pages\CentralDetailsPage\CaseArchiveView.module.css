/* متغيرات التصميم */
:root {
  --paper-bg: #ffffff;
  --paper-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --border-color: #e5e7eb;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --accent-color: #3b82f6;
  --section-bg: #f9fafb;
  --header-bg: #f3f4f6;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --border-radius: 8px;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;

  /* لوحة الألوان الحمراء الإضافية */
  --red-primary: #622872; /* بنفسجي داكن */
  --red-secondary: #caa5cb; /* وردي فاتح */
  --red-light: #e8ddea; /* وردي باهت */
  --red-lightest: #faeaf6; /* وردي فاتح جداً */

  /* لوحة الألوان الزرقاء الإضافية */
  --blue-darkest: #00033a; /* أزرق داكن جداً */
  --blue-dark-alt: #162647; /* أزرق داكن بديل */
  --blue-medium-alt: #163473; /* أزرق متوسط بديل */
  --accent-gold: #d2ab17; /* ذهبي */
}

/* الحاوي الرئيسي */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: var(--spacing-lg);
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

/* الوثيقة الورقية */
.paperDocument {
  background: var(--paper-bg);
  max-width: 800px;
  width: 100%;
  min-height: 1000px;
  box-shadow: var(--paper-shadow);
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
  margin: 0 auto;
  position: relative;
  border: 2px solid var(--border-color);
}

/* رأس الوثيقة */
.documentHeader {
  text-align: center;
  border-bottom: 3px solid var(--accent-color);
  padding-bottom: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.documentTitle {
  font-size: var(--font-size-2xl);
  font-weight: bold;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  text-decoration: underline;
  text-decoration-color: var(--accent-color);
}

.documentDate {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-style: italic;
}

/* أقسام الورقة */
.paperSection {
  margin-bottom: var(--spacing-xl);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--section-bg);
  overflow: hidden;
}

.sectionHeader {
  background: var(--header-bg);
  padding: var(--spacing-md);
  border-bottom: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.sectionHeader h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--text-primary);
}

.sectionIcon {
  color: var(--accent-color);
  font-size: var(--font-size-xl);
}

/* شبكة المعلومات */
.infoGrid {
  padding: var(--spacing-lg);
}

.infoRow {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.infoRow:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label {
  font-weight: bold;
  color: var(--text-primary);
  min-width: 180px;
  flex-shrink: 0;
  font-size: var(--font-size-base);
  line-height: 1.5;
}

.value {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: 1.5;
  word-wrap: break-word;
  flex: 1;
}

/* قائمة الأرشيف */
.archiveList {
  padding: var(--spacing-lg);
}

.archiveItem {
  background: var(--paper-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
}

.archiveItem:last-child {
  margin-bottom: 0;
}

.itemHeader {
  background: var(--header-bg);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.itemDate {
  font-weight: bold;
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.itemType {
  background: var(--accent-color);
  color: white;
  padding: 4px var(--spacing-sm);
  border-radius: 4px;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.itemContent {
  padding: var(--spacing-md);
}

/* حالة عدم وجود بيانات */
.noArchiveData {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.noDataIcon {
  font-size: 3rem;
  color: var(--border-color);
  margin-bottom: var(--spacing-md);
}

.noArchiveData p {
  font-size: var(--font-size-lg);
  margin: 0;
}

/* حالات التحميل والخطأ */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  margin: 0;
}

.errorMessage {
  background: #fee2e2;
  color: #dc2626;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  border: 2px solid #fecaca;
  text-align: center;
  font-size: var(--font-size-base);
}

/* تصميم الطباعة */
@media print {
  .container {
    background: white;
    padding: 0;
  }

  .paperDocument {
    box-shadow: none;
    border: none;
    max-width: none;
    width: 100%;
    margin: 0;
    padding: 20px;
  }

  .sectionHeader {
    background: white !important;
    border-bottom: 2px solid black;
  }

  .paperSection {
    background: white !important;
    border: 2px solid black;
    page-break-inside: avoid;
  }

  .archiveItem {
    page-break-inside: avoid;
  }
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-md);
  }

  .paperDocument {
    padding: var(--spacing-lg);
  }

  .documentTitle {
    font-size: var(--font-size-xl);
  }

  .label {
    min-width: 140px;
    font-size: var(--font-size-sm);
  }

  .value {
    font-size: var(--font-size-sm);
  }

  .infoRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .itemHeader {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--spacing-sm);
  }

  .paperDocument {
    padding: var(--spacing-md);
  }

  .documentHeader {
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
  }

  .paperSection {
    margin-bottom: var(--spacing-lg);
  }

  .sectionHeader {
    padding: var(--spacing-sm);
  }

  .infoGrid, .archiveList {
    padding: var(--spacing-md);
  }

  .itemContent {
    padding: var(--spacing-sm);
  }
}
