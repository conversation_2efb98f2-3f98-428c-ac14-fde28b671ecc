rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // القاعدة العامة: افتراضياً ممنوع أي قراءة أو كتابة على أي مستند
    match /{document=**} {
      allow read, write: if false;
    }

    // قواعد خاصة بمجموعة 'users'
    match /users/{userId} {
      allow create: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && request.auth.uid == userId;
      allow delete: if false;
    }

    // قواعد مجموعة 'cases'
    match /cases/{caseId} {
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow read, update, delete: if request.auth != null && resource.data.userId == request.auth.uid;

      match /deferrals/{deferralId} {
        allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
        allow read, update, delete: if request.auth != null && resource.data.userId == request.auth.uid;

        match /history/{historyId} {
          allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
          allow read, update, delete: if request.auth != null && resource.data.userId == request.auth.uid;
        }
      }

      match /actions/{actionId} {
        allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
        allow read, update, delete: if request.auth != null && resource.data.userId == request.auth.uid;
      }

      match /actions_archive/{archiveId} {
        allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
        allow read: if request.auth != null && request.auth.uid == resource.data.userId;
        allow update, delete: if request.auth != null && resource.data.userId == request.auth.uid;
      }

      match /deferrals_archive/{archiveId} {
        allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
        allow read: if request.auth != null && request.auth.uid == resource.data.userId;
        allow update, delete: if request.auth != null && resource.data.userId == request.auth.uid;
      }
    }

    // قواعد مجموعة 'deferralTemplates'
    match /deferralTemplates/{templateId} {
      // السماح بالقراءة والكتابة على مستوى الوثيقة
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && resource.data.userId == request.auth.uid;
      allow delete: if request.auth != null && resource.data.userId == request.auth.uid;
      // السماح بجلب قائمة الوثائق (list) على مستوى المجموعة
      allow list: if request.auth != null;
    }

    match /stats/{docId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == null; // للسماح لـ Cloud Functions
    }

    // قواعد خاصة بالمجموعات
    match /groups/{groupId} {
      // السماح بالقراءة لأي مستخدم مصادق عليه
      allow read: if request.auth != null;

      // السماح بإنشاء مجموعة جديدة إذا كان المستخدم مصادق عليه
      allow create: if request.auth != null;

      // السماح بتحديث المجموعة إذا كان المستخدم هو منشئها أو مدير
      allow update: if request.auth != null &&
                     (resource.data.createdBy == request.auth.uid ||
                      resource.data.managers[request.auth.uid] == true);

      // السماح بحذف المجموعة إذا كان المستخدم هو منشئها
      allow delete: if request.auth != null &&
                     resource.data.createdBy == request.auth.uid;
    }

    // قواعد خاصة بأعضاء المجموعات
    match /members/{memberId} {
      // السماح بالقراءة لأي مستخدم مصادق عليه
      allow read: if request.auth != null;

      // السماح بإضافة عضو جديد إذا كان المستخدم مصادق عليه
      allow create: if request.auth != null;

      // السماح بتحديث بيانات العضو إذا كان المستخدم هو منشئ المجموعة أو مدير أو العضو نفسه
      allow update: if request.auth != null &&
                     (resource.data.userId == request.auth.uid ||
                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);

      // السماح بحذف العضو إذا كان المستخدم هو منشئ المجموعة أو مدير أو العضو نفسه
      allow delete: if request.auth != null &&
                     (resource.data.userId == request.auth.uid ||
                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد خاصة بالقضايا المرتبطة بالمجموعات
    match /groupCases/{caseId} {
      // السماح بالقراءة لأي عضو في المجموعة
      allow read: if request.auth != null &&
                   exists(/databases/$(database)/documents/members/$(request.auth.uid + '_' + resource.data.groupId));

      // السماح بالإنشاء والتحديث والحذف لمنشئ المجموعة والمديرين
      allow create, update, delete: if request.auth != null &&
                                     (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد خاصة بالإشعارات المرتبطة بالمجموعات
    match /groupNotifications/{notificationId} {
      // السماح بالقراءة فقط للمستخدم المرسل إليه الإشعار
      allow read: if request.auth != null &&
                   resource.data.userId == request.auth.uid;

      // السماح بالكتابة لمنشئ المجموعة والمديرين
      allow write: if request.auth != null &&
                    (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                     get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }

    // قواعد خاصة بالمهام في المجموعات
    match /groupTasks/{taskId} {
      // السماح بالقراءة لأي عضو في المجموعة
      allow read: if request.auth != null &&
                   exists(/databases/$(database)/documents/members/$(request.auth.uid + '_' + resource.data.groupId));

      // السماح بالإنشاء والتحديث والحذف لمنشئ المجموعة والمديرين
      allow create, update, delete: if request.auth != null &&
                                     (get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.createdBy == request.auth.uid ||
                                      get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.managers[request.auth.uid] == true);
    }
  }
}