import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { FaChevronLeft, FaRedo, Fa<PERSON>lock, FaClipboardList, FaInfoCircle, FaUser, FaGavel, FaFileAlt, FaCalendarAlt } from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import CaseInfoGroups from './CentralDetailsPage/CaseInfoGroups';
import ReportHistory from './CentralDetailsPage/ReportHistory';
import AddDeferral from './CentralDetailsPage/AddDeferral';
import AddAction from './CentralDetailsPage/AddAction';
import CaseFollowUpModal from './CentralDetailsPage/CaseFollowUpModal';
import styles from './CentralDetailsPage/CaseDetailsNew.module.css';
import { handleAddAction } from './CentralDetailsPage/ReportDetailsLogic';
import { cacheManager, notifyTaskCreated } from '../utils/CacheManager';
import { getActiveAccount, getCase, updateCase } from '../services/StorageService';

const CaseDetails = ({ currentUser }) => {
  const { caseNumber } = useParams();
  const caseId = caseNumber;

  const [caseData, setCaseData] = useState(null);
  const [loadingCase, setLoadingCase] = useState(true);
  const [caseError, setCaseError] = useState(null);
  const [deferrals, setDeferrals] = useState([]);
  const [actions, setActions] = useState([]);
  const [history, setHistory] = useState([]);
  const [showForm, setShowForm] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // دالة لتحديث بيانات القضية عند التعديل
  const handleCaseDataUpdate = (updateData) => {
    setCaseData(prevData => {
      const updatedData = {
        ...prevData,
        ...updateData
      };

      // تحديث الكاش المحلي باستخدام CacheManager
      try {
        cacheManager.updateCache(CACHE_KEY, updatedData, CACHE_TTL);
      } catch (error) {
        console.error('Error updating cache:', error);
      }

      // إذا تم تحديث حالة القضية، قم بإعادة تحميل البيانات بدلاً من إعادة تحميل الصفحة
      if (updateData.caseStatus && updateData.caseStatus !== prevData.caseStatus) {
        setTimeout(() => {
          fetchCaseDataAndDetails(true); // إعادة تحميل البيانات فقط
        }, 500);
      }

      return updatedData;
    });
  };

  const CACHE_KEY = `case_${caseId}`;
  const CACHE_TTL = 30 * 1000; // 30 seconds for faster updates



  const fetchCaseDataAndDetails = async (bypassCache = false) => {
    // التحقق من الحساب النشط
    const activeAccount = getActiveAccount();

    // إذا كان الحساب أونلاين وغير متصل بالإنترنت
    if (activeAccount === 'online' && !navigator.onLine) {
      setCaseError('غير متصل بالإنترنت. سيتم تحميل البيانات عند استعادة الاتصال أو يمكنك التبديل إلى الحساب المحلي.');
      setLoadingCase(false);
      return;
    }

    // استخدام التخزين المؤقت إذا كان متاحًا
    if (!bypassCache) {
      const cachedData = cacheManager.getCache(CACHE_KEY);
      if (cachedData) {
        setCaseData(cachedData);
        setDeferrals(cachedData.deferrals || []);
        setActions(cachedData.actions || []);
        setHistory(cachedData.history || []);
        setLoadingCase(false);
        return;
      }
    }

    setLoadingCase(true);
    setCaseError(null);

    if (!caseId) {
      setCaseError("لم يتم تحديد معرف القضية في المسار.");
      setLoadingCase(false);
      return;
    }

    if (!currentUser) {
      setCaseError("يجب تسجيل الدخول لعرض تفاصيل القضايا.");
      setLoadingCase(false);
      return;
    }

    try {
      // استخدام خدمة التخزين المحلي للحصول على بيانات القضية حسب الحساب النشط
      const caseData = await getCase(currentUser.uid, caseId);

      if (caseData) {
        if (caseData.userId !== currentUser.uid) {
          setCaseError("لا تمتلك صلاحية عرض تفاصيل هذه القضية.");
          setCaseData(null);
        } else {
          setCaseData(caseData);
          setDeferrals(caseData.deferrals || []);
          setActions(caseData.actions || []);
          setHistory(caseData.history || []);

          // تخزين البيانات في التخزين المؤقت باستخدام CacheManager
          cacheManager.updateCache(CACHE_KEY, caseData, CACHE_TTL);
        }
      } else {
        // رسالة خطأ مختلفة حسب الحساب النشط
        if (activeAccount === 'online') {
          setCaseError("لم يتم العثور على بيانات القضية في الحساب الأونلاين.");
        } else {
          setCaseError("لم يتم العثور على بيانات القضية في الحساب المحلي.");
        }
        setCaseData(null);
      }
    } catch (error) {
      console.error("Error fetching case details:", error);

      if (activeAccount === 'online' && error.code === 'unavailable') {
        setCaseError('غير متصل بالإنترنت. سيتم تحميل البيانات عند استعادة الاتصال أو يمكنك التبديل إلى الحساب المحلي.');
      } else {
        setCaseError("حدث خطأ أثناء جلب بيانات القضية: " + error.message);
      }
      setCaseData(null);
    } finally {
      setLoadingCase(false);
    }
  };

  useEffect(() => {
    fetchCaseDataAndDetails();

    // إضافة مستمع للتحديثات الفورية
    const refreshListener = ({ caseId: updatedCaseId }) => {
      if (updatedCaseId === caseId) {
        fetchCaseDataAndDetails(true);
      }
    };

    // إضافة مستمع لفتح نافذة متابعة الملف من كارت تحويل الحالة
    const openCaseFollowUpListener = (event) => {
      const { action } = event.detail || {};
      setShowFollowUpModal(true);

      // يمكن إضافة منطق لتحديد الإجراء المطلوب مسبقاً
      if (action === 'change_status') {
        // تحديد الإجراء المطلوب مسبقاً
      }
    };

    // إعادة تحميل البيانات عند العودة للصفحة
    const handleFocus = () => {
      fetchCaseDataAndDetails(true);
    };

    cacheManager.addListener('case_refresh', refreshListener);
    window.addEventListener('openCaseFollowUp', openCaseFollowUpListener);
    window.addEventListener('focus', handleFocus);

    return () => {
      cacheManager.removeListener('case_refresh', refreshListener);
      window.removeEventListener('openCaseFollowUp', openCaseFollowUpListener);
      window.removeEventListener('focus', handleFocus);
    };
  }, [caseId, currentUser]);

  const handleBack = () => {
    window.location.href = '/reports';
  };

  const handleRetry = () => {
    fetchCaseDataAndDetails(true);
  };

  const handleCaseFollowUp = () => {
    setShowFollowUpModal(true);
  };

  const handleCancelForm = () => {
    setShowForm(null);
  };

  const handleSaveDeferral = async (reportDate, selectedReasons, deferralDescription, setError) => {
    try {
      if (!reportDate || isNaN(new Date(reportDate).getTime()) || selectedReasons.length === 0) {
        setError("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل");
        return;
      }

      // إنشاء التأجيل الجديد - يُضاف لقائمة المهام فقط وليس للأرشيف
      const newDeferral = {
        id: `${caseData.id}-defer-${Date.now()}`,
        date: reportDate,
        reasons: selectedReasons,
        description: deferralDescription || '', // إضافة الوصف
        createdAt: new Date().toISOString(),
        isDeleted: false,
        isArchived: false, // لا يُضاف للأرشيف تلقائياً
      };

      // تحديث قائمة التأجيلات
      const updatedDeferrals = [...deferrals, newDeferral];
      setDeferrals(updatedDeferrals);

      // حفظ البيانات في قاعدة البيانات
      await updateCase(currentUser.uid, caseData.id, {
        deferrals: updatedDeferrals,
      });

      alert('تم إضافة التنبيه بتاريخ الجلسة بنجاح');

      // إشعار مدير التخزين المؤقت بإنشاء مهمة جديدة
      notifyTaskCreated(currentUser.uid);

      handleCancelForm();

    } catch (err) {
      console.error("Error in handleSaveDeferral:", err);
      setError(err.message);
    }
  };

  const handleSaveAction = async (newAction, actionDeadline, linkType, linkedDeferralId, linkedActionId, reminderType, setError) => {
    try {
      const updatedCaseData = { ...caseData, deferrals };
      await handleAddAction(
        newAction,
        actionDeadline,
        linkType,
        linkedDeferralId,
        linkedActionId,
        reminderType,
        updatedCaseData,
        actions,
        setActions,
        () => {},
        () => {},
        () => {},
        () => {},
        () => {},
        () => {},
        setHistory
      );

      // إشعار مدير التخزين المؤقت بإنشاء مهمة جديدة
      notifyTaskCreated(currentUser.uid);

      // لا حاجة لإعادة التحميل - البيانات محدثة في الحالة
    } catch (err) {
      console.error("Error in handleSaveAction:", err);
      setError(err.message);
    }
  };

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth <= 768);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (loadingCase) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} casesList={[]} />
        <div className={styles.mainContainer}>
          <div className={styles.loadingMessage}>
            <div className={styles.loadingIcon}>
              <div className={styles.spinner}></div>
            </div>
            جاري تحميل تفاصيل القضية...
          </div>
        </div>
      </div>
    );
  }

  if (caseError) {
    return (
      <div className={styles.pageWrapper}>
        <TopBar currentUser={currentUser} casesList={[]} />
        <div className={styles.mainContainer}>
          <div className={styles.errorMessage}>
            <FaInfoCircle className={styles.errorIcon} />
            {caseError}
          </div>
          <div className={styles.buttonsSection}>
            <button onClick={handleBack} className={styles.backButton}>
              <FaChevronLeft className={styles.buttonIcon} />
              العودة
            </button>
            <button onClick={handleRetry} className={styles.editButton}>
              <FaRedo className={styles.buttonIcon} />
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    );
  }

  let pageTitle = 'تفاصيل القضية';
  let showCaseNumber = true;

  if (caseData.caseStatus === 'قيد النظر') {
    pageTitle = 'التفاصيل';
    showCaseNumber = false;
  } else if (caseData.caseStatus === 'محضر') {
    pageTitle = 'تفاصيل المحضر';
    showCaseNumber = false;
  } else if (caseData.caseStatus === 'دعوى قضائية') {
    pageTitle = 'تفاصيل الدعوى';
  }

  const isUnderConsideration = caseData?.caseStatus === 'قيد النظر';

  // تحديد فئة CSS حسب حالة القضية
  const statusClass = caseData.caseStatus?.replace(/\s+/g, '');

  // اختيار الأيقونة المناسبة حسب نوع القضية
  const getCaseIcon = () => {
    if (caseData.caseStatus === 'دعوى قضائية') {
      return <FaGavel className={styles.caseStatusIcon} />;
    } else if (caseData.caseStatus === 'محضر') {
      return <FaFileAlt className={styles.caseStatusIcon} />;
    } else {
      return <FaInfoCircle className={styles.caseStatusIcon} />;
    }
  };

  return (
    <div className={styles.pageWrapper}>
      <TopBar currentUser={currentUser} casesList={[]} />
      <div className={styles.mainContainer}>
        {/* قسم العنوان */}
        <div className={`${styles.headerSection} ${statusClass}`}>
          {/* خانة الإحالة في أقصى اليسار والأعلى داخل الهدر */}
          <div className={styles.topLeftExpertBox}>
            {/* يمكن إضافة محتوى إضافي هنا مستقبلاً */}
          </div>

          <div className={styles.headerContent}>
            <div className={styles.headerLayout}>
              <div className={styles.titleSection}>
                {getCaseIcon()}
                <h1 className={`${styles.caseTitle} ${statusClass}`}>
                  {pageTitle}
                  {showCaseNumber && caseData.fullCaseNumber && caseData.fullCaseNumber !== '—'
                    ? ` - ${caseData.fullCaseNumber}`
                    : ''}
                </h1>
                {caseData.clientName && (
                  <p className={styles.caseSubtitle}>
                    <FaUser className={styles.subtitleIcon} /> الموكل: {caseData.clientName}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* تخطيط المحتوى الجديد - عمودين */}
        <div className={styles.twoColumnLayout}>
          {/* العمود الأيمن - بيانات القضية + الملاحظات وسجل المهام */}
          <div className={styles.rightColumn}>
            <div className={styles.caseInfoGrid}>
              <CaseInfoGroups
                caseData={caseData}
                currentUser={currentUser}
                onCaseDataUpdate={handleCaseDataUpdate}
              />
            </div>
            {/* حاوية جانبية للملاحظات وسجل المهام */}
            <div
              style={{
                display: 'flex',
                gap: isMobile ? '1.5rem' : '2.5rem',
                alignItems: 'stretch',
                marginTop: '1.5rem',
                flexDirection: isMobile ? 'column' : 'row'
              }}
            >
              <div style={{ flex: 2, width: '100%' }}>
                <ReportHistory currentUser={currentUser} actions={actions} deferrals={deferrals} history={history} />
              </div>
              <div style={{ flex: 1, width: '100%' }}>
                <CaseFollowUpModal
                  caseId={caseId}
                  userId={currentUser.uid}
                  savedNotes={caseData?.notes || []}
                  onClose={() => {}}
                  onSave={(data) => {
                    setCaseData(prevData => ({
                      ...prevData,
                      notes: [...(prevData.notes || []), data]
                    }));
                  }}
                />
              </div>
            </div>
          </div>

          {/* العمود الأيسر - معلومات إضافية */}
          <div className={styles.leftColumn}>
            {/* يمكن إضافة محتوى إضافي هنا مستقبلاً */}
          </div>
        </div>        {/* قسم الأزرار والنماذج */}
        <div className={styles.actionsSection}>
            {/* تم حذف النسخة القديمة من صفحة الملاحظات هنا */}
            {showForm === 'deferral' && (
              <AddDeferral
                currentUser={currentUser}
                onSave={handleSaveDeferral}
                onCancel={handleCancelForm}
                isUnderConsideration={isUnderConsideration}
              />
            )}
            {showForm === 'action' && (
              <AddAction
                currentUser={currentUser}
                caseItem={caseData}
                deferrals={deferrals}
                actions={actions}
                setActions={setActions}
                history={history}
                setHistory={setHistory}
                onSave={handleSaveAction}
                onCancel={handleCancelForm}
              />
            )}
        </div>

        {/* قسم أزرار التنقل */}
        <div className={styles.buttonsSection}>
          <button onClick={handleBack} className={styles.backButton}>
            <FaChevronLeft className={styles.buttonIcon} />
            <span>عودة</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default CaseDetails;
