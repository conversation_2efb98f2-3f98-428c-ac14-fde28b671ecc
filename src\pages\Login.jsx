import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './Login.css';
import { auth } from '../config/firebaseConfig';
import { signInWithEmailAndPassword, sendPasswordResetEmail } from "firebase/auth";
import { checkLoginLockout, recordLoginFailure, clearLoginLockout } from '../utils/LockoutManager';

function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    const lockoutStatus = checkLoginLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(true);
    } else {
      setLoginAttempts(lockoutStatus.attempts);
    }
  }, []);

  const handleSubmit = async (event) => {
    event.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);

    if (!email.trim() || !password.trim()) {
      setErrorMessage('يرجى إدخال البريد الإلكتروني وكلمة المرور.');
      setLoading(false);
      return;
    }

    // التحقق من حالة القفل قبل المحاولة
    const lockoutStatus = checkLoginLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(false);
      return;
    }

    try {
      const userCredential = await signInWithEmailAndPassword(auth, email.trim(), password);
      const user = userCredential.user;
      console.log('تم تسجيل الدخول بنجاح:', user);
      // مسح بيانات القفل عند النجاح
      clearLoginLockout();
      setLoginAttempts(0);
      navigate('/dashboard', { replace: true });
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error.code, error.message);

      // تسجيل المحاولة الفاشلة
      const failureResult = recordLoginFailure(loginAttempts);
      setLoginAttempts(failureResult.attempts);

      if (failureResult.isLocked) {
        setErrorMessage(failureResult.message);
        setLoading(true);
        return;
      }

      let userFacingMessage = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.';
      switch (error.code) {
        case 'auth/invalid-credential':
          userFacingMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى التحقق من البيانات أو إعادة تعيين كلمة المرور.';
          break;
        case 'auth/invalid-email':
          userFacingMessage = 'صيغة البريد الإلكتروني غير صحيحة.';
          break;
        case 'auth/user-disabled':
          userFacingMessage = 'تم تعطيل حساب المستخدم هذا.';
          break;
        case 'auth/too-many-requests':
          userFacingMessage = 'تم حظر تسجيل الدخول مؤقتًا بسبب محاولات كثيرة جدًا. يرجى المحاولة لاحقًا.';
          break;
        default:
          userFacingMessage = 'حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا.';
      }
      setErrorMessage(userFacingMessage);
    } finally {
      if (attempts < MAX_ATTEMPTS) {
        setLoading(false);
      }
    }
  };

  const handlePasswordReset = async () => {
    if (!email.trim()) {
      setErrorMessage('يرجى إدخال البريد الإلكتروني أولاً.');
      return;
    }
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);
    try {
      await sendPasswordResetEmail(auth, email.trim());
      setSuccessMessage('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد أو البريد العشوائي.');
    } catch (error) {
      let userFacingMessage = 'حدث خطأ أثناء إرسال رابط إعادة التعيين.';
      switch (error.code) {
        case 'auth/invalid-email':
          userFacingMessage = 'صيغة البريد الإلكتروني غير صحيحة.';
          break;
        case 'auth/user-not-found':
          userFacingMessage = 'لا يوجد حساب مرتبط بهذا البريد الإلكتروني.';
          break;
        default:
          userFacingMessage = 'حدث خطأ غير متوقع. يرجى المحاولة لاحقًا.';
      }
      setErrorMessage(userFacingMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-box">
        <h2>تسجيل الدخول</h2>

        {loading && (
          <div className="loading-container">
            <div className="loader"></div>
            <p className="loading-text">جاري المعالجة...</p>
          </div>
        )}

        {errorMessage && <div className="error-message">{errorMessage}</div>}
        {successMessage && <div className="success-message">{successMessage}</div>}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">البريد الإلكتروني</label>
            <input
              type="email"
              id="email"
              name="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={loading}
              placeholder="أدخل بريدك الإلكتروني"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">كلمة المرور</label>
            <input
              type="password"
              id="password"
              name="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
              placeholder="أدخل كلمة المرور"
            />
          </div>

          <button type="submit" disabled={loading}>
            {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
          </button>
        </form>

        <div className="login-links">
          <p>
            <a href="#" onClick={(e) => { e.preventDefault(); handlePasswordReset(); }}>
              نسيت كلمة المرور؟
            </a>
          </p>
          <p>
            ليس لديك حساب؟ <a href="/signup">إنشاء حساب جديد</a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default Login;