// خدمة إدارة المجموعات والأعضاء في Firebase
import { db } from '../config/firebaseConfig';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  doc, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  serverTimestamp,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';

// اسماء المجموعات في Firestore
const GROUPS_COLLECTION = 'groups';
const MEMBERS_COLLECTION = 'members';

/**
 * الحصول على جميع المجموعات للمستخدم
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<Array>} - مصفوفة من المجموعات
 */
export const getGroups = async (userId) => {
  try {
    // البحث عن المجموعات التي أنشأها المستخدم أو هو عضو فيها
    const groupsRef = collection(db, GROUPS_COLLECTION);
    const q = query(groupsRef, where('createdBy', '==', userId));
    const querySnapshot = await getDocs(q);
    
    // تحويل البيانات إلى مصفوفة
    const groups = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      created: doc.data().createdAt?.toDate().toISOString().split('T')[0] || new Date().toISOString().split('T')[0]
    }));
    
    // البحث عن المجموعات التي المستخدم عضو فيها
    const membersRef = collection(db, MEMBERS_COLLECTION);
    const memberQ = query(membersRef, where('userId', '==', userId));
    const memberSnapshot = await getDocs(memberQ);
    
    // الحصول على معرفات المجموعات التي المستخدم عضو فيها
    const memberGroupIds = memberSnapshot.docs.map(doc => doc.data().groupId);
    
    // الحصول على بيانات المجموعات التي المستخدم عضو فيها
    const memberGroups = [];
    for (const groupId of memberGroupIds) {
      if (!groups.some(g => g.id === groupId)) { // تجنب التكرار
        const groupDoc = await getDoc(doc(db, GROUPS_COLLECTION, groupId));
        if (groupDoc.exists()) {
          memberGroups.push({
            id: groupDoc.id,
            ...groupDoc.data(),
            created: groupDoc.data().createdAt?.toDate().toISOString().split('T')[0] || new Date().toISOString().split('T')[0]
          });
        }
      }
    }
    
    return [...groups, ...memberGroups];
  } catch (error) {
    console.error('خطأ في جلب المجموعات:', error);
    return [];
  }
};

/**
 * إنشاء مجموعة جديدة
 * @param {string} userId - معرف المستخدم
 * @param {Object} groupData - بيانات المجموعة
 * @returns {Promise<Object>} - المجموعة المنشأة
 */
export const createGroup = async (userId, groupData) => {
  try {
    const groupsRef = collection(db, GROUPS_COLLECTION);
    
    // إضافة بيانات إضافية للمجموعة
    const newGroupData = {
      ...groupData,
      createdBy: userId,
      createdAt: serverTimestamp(),
      members: 1, // المستخدم الحالي كمدير
      updatedAt: serverTimestamp()
    };
    
    // إضافة المجموعة إلى Firestore
    const docRef = await addDoc(groupsRef, newGroupData);
    
    // إضافة المستخدم كمدير للمجموعة
    const membersRef = collection(db, MEMBERS_COLLECTION);
    await addDoc(membersRef, {
      groupId: docRef.id,
      userId: userId,
      role: 'admin', // مدير
      name: groupData.creatorName || 'المستخدم الحالي',
      email: groupData.creatorEmail || 'غير متوفر',
      joinedAt: serverTimestamp()
    });
    
    return {
      id: docRef.id,
      ...newGroupData,
      created: new Date().toISOString().split('T')[0]
    };
  } catch (error) {
    console.error('خطأ في إنشاء المجموعة:', error);
    throw error;
  }
};

/**
 * حذف مجموعة
 * @param {string} groupId - معرف المجموعة
 * @returns {Promise<void>}
 */
export const deleteGroup = async (groupId) => {
  try {
    // حذف المجموعة
    await deleteDoc(doc(db, GROUPS_COLLECTION, groupId));
    
    // حذف جميع الأعضاء في المجموعة
    const membersRef = collection(db, MEMBERS_COLLECTION);
    const q = query(membersRef, where('groupId', '==', groupId));
    const querySnapshot = await getDocs(q);
    
    const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(deletePromises);
  } catch (error) {
    console.error('خطأ في حذف المجموعة:', error);
    throw error;
  }
};

/**
 * الحصول على أعضاء المجموعة
 * @param {string} groupId - معرف المجموعة (اختياري)
 * @returns {Promise<Array>} - مصفوفة من الأعضاء
 */
export const getMembers = async (groupId = null) => {
  try {
    const membersRef = collection(db, MEMBERS_COLLECTION);
    let q;
    
    if (groupId) {
      // الحصول على أعضاء مجموعة محددة
      q = query(membersRef, where('groupId', '==', groupId));
    } else {
      // الحصول على جميع الأعضاء
      q = query(membersRef);
    }
    
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinDate: doc.data().joinedAt?.toDate().toISOString().split('T')[0] || new Date().toISOString().split('T')[0]
    }));
  } catch (error) {
    console.error('خطأ في جلب الأعضاء:', error);
    return [];
  }
};

/**
 * إضافة عضو جديد إلى مجموعة
 * @param {Object} memberData - بيانات العضو
 * @returns {Promise<Object>} - العضو المضاف
 */
export const addMember = async (memberData) => {
  try {
    // التحقق من وجود المجموعة
    const groupRef = doc(db, GROUPS_COLLECTION, memberData.groupId);
    const groupDoc = await getDoc(groupRef);
    
    if (!groupDoc.exists()) {
      throw new Error('المجموعة غير موجودة');
    }
    
    // إضافة العضو
    const membersRef = collection(db, MEMBERS_COLLECTION);
    const newMemberData = {
      ...memberData,
      joinedAt: serverTimestamp()
    };
    
    const docRef = await addDoc(membersRef, newMemberData);
    
    // تحديث عدد الأعضاء في المجموعة
    await updateDoc(groupRef, {
      members: (groupDoc.data().members || 0) + 1,
      updatedAt: serverTimestamp()
    });
    
    return {
      id: docRef.id,
      ...newMemberData,
      joinDate: new Date().toISOString().split('T')[0]
    };
  } catch (error) {
    console.error('خطأ في إضافة العضو:', error);
    throw error;
  }
};

/**
 * حذف عضو من مجموعة
 * @param {string} memberId - معرف العضو
 * @param {string} groupId - معرف المجموعة
 * @returns {Promise<void>}
 */
export const deleteMember = async (memberId, groupId) => {
  try {
    // حذف العضو
    await deleteDoc(doc(db, MEMBERS_COLLECTION, memberId));
    
    // تحديث عدد الأعضاء في المجموعة
    const groupRef = doc(db, GROUPS_COLLECTION, groupId);
    const groupDoc = await getDoc(groupRef);
    
    if (groupDoc.exists()) {
      await updateDoc(groupRef, {
        members: Math.max(1, (groupDoc.data().members || 1) - 1), // لا يقل عن 1
        updatedAt: serverTimestamp()
      });
    }
  } catch (error) {
    console.error('خطأ في حذف العضو:', error);
    throw error;
  }
};
