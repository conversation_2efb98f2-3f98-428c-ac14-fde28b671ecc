/* استخدام متغيرات CSS من الملف الرئيسي */

:root {
  /* لوحة الألوان الحمراء الإضافية */
  --red-primary: #622872; /* بنفسجي داكن */
  --red-secondary: #caa5cb; /* وردي فاتح */
  --red-light: #e8ddea; /* وردي باهت */
  --red-lightest: #faeaf6; /* وردي فاتح جداً */

  /* لوحة الألوان الزرقاء الإضافية */
  --blue-darkest: #00033a; /* أزرق داكن جداً */
  --blue-dark-alt: #162647; /* أزرق داكن بديل */
  --blue-medium-alt: #163473; /* أزرق متوسط بديل */
  --accent-gold: #d2ab17; /* ذهبي */
}

/* تنسيق الـ Dashboard */
.dashboard {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

}

/* تنسيق المحتوى الرئيسي */
.content {
  flex: 1;
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  text-align: center; /* محاذاة النصوص للوسط */
}

/* تنسيق رأس الصفحة */
.header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  gap: 15px;
}

/* تنسيق حالة الحساب */
.accountStatus {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: white;
  border-radius: var(--radius-md);
  font-size: 15px;
  color: var(--neutral-700);
  flex-wrap: wrap;
  justify-content: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
}

.onlineText {
  color: var(--info);
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 600;
}

.localText {
  color: var(--success);
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 600;
}

.accountIcon {
  font-size: 16px;
}

.accountNote {
  font-size: 13px;
  color: var(--neutral-500);
  margin-left: 10px;
}

/* تنسيق الشبكة */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

/* تنسيق الكروت */
.card {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: 1.75rem;
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
  color: var(--neutral-800);
  border: 1px solid var(--neutral-200);
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--blue-medium-alt), var(--accent-gold));
}

/* تنسيق قسم الخطوات */
.stepsSection {
  margin-top: 20px;
}

.stepsSection label {
  display: block;
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 1rem;
  color: var(--text-primary);
}

/* قائمة الخطوات */
.stepsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

/* عناصر الخطوات */
.stepItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--neutral-50);
  padding: 12px 16px;
  border-radius: var(--radius-md);
  color: var(--neutral-800);
  border: 1px solid var(--neutral-200);
}

.removeStepBtn {
  background-color: var(--error);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  padding: 6px 10px;
  cursor: pointer;
  transition: all var(--transition-normal);
  width: auto;
}

.removeStepBtn:hover {
  background-color: #dc2626;
  transform: translateY(-2px);
}

.stepInputContainer {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;
}

.stepInput {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-size: 1rem;
  background-color: white;
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.stepInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.stepInput::placeholder {
  color: var(--neutral-400);
}

.addStepBtn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 12px 16px;
  cursor: pointer;
  transition: all var(--transition-normal);
  width: auto;
}

.addStepBtn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

/* التنسيقات العامة للجزء العربي */
.arabicContainer {
  direction: rtl;
  font-family: 'Tajawal', sans-serif;
  padding: 24px;
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  flex: 1;
  color: var(--neutral-800);
  border: 1px solid var(--neutral-200);
}

/* عنوان الصفحة */
.arabicHeader {
  display: flex;
  align-items: center;
  margin-bottom: 28px;
}

.arabicBackButton {
  display: flex;
  align-items: center;
  background-color: var(--neutral-100);
  padding: 10px 16px;
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--neutral-700);
  transition: all var(--transition-normal);
  border: 1px solid var(--neutral-200);
  font-weight: 500;
}

.arabicBackButton:hover {
  background-color: var(--neutral-200);
  transform: translateY(-2px);
  color: var(--neutral-900);
}

.arabicButtonIcon {
  margin-left: 8px;
}

.arabicTitle {
  margin: 0;
  font-size: 24px;
  color: var(--neutral-900);
  flex-grow: 1;
  font-weight: 600;
}

/* تقارير الجلسات */
.arabicReportsSection {
  margin-bottom: 25px;
}

/* كروت التقارير */
.arabicReportCard {
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 16px;
  border-right: 4px solid var(--accent-gold);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.arabicReportCard:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--red-primary);
}

.arabicReportDate {
  font-weight: 600;
  color: var(--neutral-900);
  margin-bottom: 8px;
  font-size: 16px;
}

.arabicReportContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arabicReportText {
  color: var(--neutral-700);
  line-height: 1.6;
  font-size: 15px;
}

.arabicHistoryButton {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-size: 16px;
  margin-right: 10px;
  transition: all var(--transition-normal);
  width: auto;
  padding: 6px 12px;
}

.arabicHistoryButton:hover {
  color: var(--primary-dark);
  background-color: rgba(37, 99, 235, 0.1);
  border-radius: var(--radius-md);
}

/* الخط الفاصل */
.arabicDivider {
  margin: 28px 0;
  display: flex;
  justify-content: center;
}

.arabicLine {
  width: 100%;
  height: 1px;
  background-color: var(--neutral-200);
}

/* قسم الإجراءات */
.arabicActionsSection {
  margin-top: 20px;
}

.arabicSectionTitle {
  font-size: 20px;
  color: var(--text-primary);
  margin-bottom: 15px;
}

/* كروت الإجراءات */
.arabicActionCard {
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.arabicActionCard:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

.arabicActionCard::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background-color: var(--blue-medium-alt);
}

.arabicActionTitle {
  font-weight: 600;
  color: var(--neutral-900);
  margin-bottom: 10px;
  font-size: 16px;
}

.arabicActionDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arabicActionDeadline {
  color: var(--neutral-600);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}



/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
  .content {
    padding: 2rem;
  }
  .grid {
    gap: 2rem;
  }
  .card {
    padding: 2rem;
  }
}

/* تحسينات للتابلت (768px - 991px) */
@media (max-width: 991px) {
  .content {
    padding: 1.25rem;
  }
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  .card {
    padding: 1.25rem;
  }
}

/* تحسينات للموبايل (أقل من 768px) */
@media (max-width: 768px) {
  .content {
    padding: 1rem;
  }
  .grid {
    grid-template-columns: 1fr; /* عمود واحد على الموبايل */
    gap: 1rem;
  }
  .card {
    padding: 1rem;
  }
  h1 {
    font-size: 1.5rem; /* تقليل حجم العنوان */
  }
  p {
    font-size: 0.9rem; /* تقليل حجم النص */
  }
}

/* شاشات صغيرة جدًا (أقل من 576px) */
@media (max-width: 576px) {
  .content {
    padding: 0.75rem;
  }
  .grid {
    margin-top: 1.5rem;
  }
  .card {
    padding: 0.75rem;
  }
  h1 {
    font-size: 1.25rem;
  }
  p {
    font-size: 0.85rem;
  }
}