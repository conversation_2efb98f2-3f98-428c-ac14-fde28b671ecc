/* Transfer Degree Box Container */
.transferDegreeBox {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #3b82f6;
  border-radius: 8px;
  font-size: 0.85rem;
  box-shadow: 
    0 2px 6px rgba(59, 130, 246, 0.15),
    0 0 20px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  max-width: 300px;
  margin-left: 16px;
  position: relative;
  overflow: hidden;
}

/* تأثير اللمعان المتحرك */
.transferDegreeBox::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    transform: rotate(45deg) translate(-100%, -100%);
  }
  50% {
    transform: rotate(45deg) translate(0%, 0%);
  }
  100% {
    transform: rotate(45deg) translate(100%, 100%);
  }
}

.transferDegreeBox:hover {
  box-shadow: 
    0 4px 12px rgba(59, 130, 246, 0.25),
    0 0 30px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
  border-color: #2563eb;
}

/* Header Section */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02));
  position: relative;
  overflow: hidden;
  border-radius: 6px;
}

/* تأثير لمعان العنوان */
.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s ease;
}

.header:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.header:hover::before {
  left: 100%;
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 6px;
}

.icon {
  color: #3b82f6;
  font-size: 0.9rem;
  filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
  transition: all 0.3s ease;
}

.title {
  font-weight: 600;
  color: #1e40af;
  font-size: 0.85rem;
  text-shadow: 0 0 4px rgba(30, 64, 175, 0.2);
  transition: all 0.3s ease;
}

.expandIcon {
  color: #6b7280;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 1px rgba(107, 114, 128, 0.3));
}

.header:hover .icon {
  color: #2563eb;
  filter: drop-shadow(0 0 4px rgba(37, 99, 235, 0.5));
  transform: scale(1.1);
}

.header:hover .title {
  color: #1d4ed8;
  text-shadow: 0 0 6px rgba(29, 78, 216, 0.3);
}

.header:hover .expandIcon {
  color: #3b82f6;
  filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.4));
  transform: scale(1.1);
}

/* Content Section */
.content {
  padding: 12px;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  animation: slideDown 0.2s ease-out;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Info Rows */
.infoRow {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 0.8rem;
  line-height: 1.4;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.infoRow:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02));
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
  transform: translateX(2px);
}

.infoRow:last-child {
  margin-bottom: 0;
}

.rowIcon {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 2px;
  flex-shrink: 0;
  filter: drop-shadow(0 0 1px rgba(107, 114, 128, 0.3));
  transition: all 0.3s ease;
}

.infoRow:hover .rowIcon {
  color: #3b82f6;
  filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.4));
  transform: scale(1.1);
}

.label {
  font-weight: 500;
  color: #374151;
  flex-shrink: 0;
  min-width: 80px;
  text-shadow: 0 0 2px rgba(55, 65, 81, 0.1);
  transition: all 0.3s ease;
}

.infoRow:hover .label {
  color: #1f2937;
  text-shadow: 0 0 3px rgba(31, 41, 55, 0.2);
}

.value {
  color: #1f2937;
  flex: 1;
  word-break: break-word;
  text-shadow: 0 0 1px rgba(31, 41, 55, 0.1);
  transition: all 0.3s ease;
}

.infoRow:hover .value {
  color: #111827;
  text-shadow: 0 0 2px rgba(17, 24, 39, 0.2);
}

/* Transfer Info Section */
.transferInfo {
  margin: 12px 0;
  padding: 8px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02));
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.transferDescription {
  margin: 0;
  font-size: 0.75rem;
  color: #4b5563;
  line-height: 1.4;
  text-align: justify;
}

/* Action Row */
.actionRow {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.transferButton {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.transferButton:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.transferButton:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .transferDegreeBox {
    max-width: 100%;
    margin-left: 0;
    margin-top: 8px;
    font-size: 0.8rem;
  }
  
  .header {
    padding: 6px 10px;
  }
  
  .title {
    font-size: 0.8rem;
  }
  
  .content {
    padding: 10px;
  }
  
  .infoRow {
    flex-direction: column;
    gap: 2px;
    margin-bottom: 10px;
  }
  
  .label {
    min-width: auto;
    font-size: 0.75rem;
  }
  
  .value {
    font-size: 0.75rem;
    margin-right: 8px;
  }
  
  .transferButton {
    font-size: 0.75rem;
    padding: 6px 12px;
  }
  
  .transferDescription {
    font-size: 0.7rem;
  }
}

/* Compact Mode */
.transferDegreeBox.compact {
  max-width: 250px;
  font-size: 0.8rem;
}

.transferDegreeBox.compact .header {
  padding: 6px 10px;
}

.transferDegreeBox.compact .title {
  font-size: 0.8rem;
}

.transferDegreeBox.compact .content {
  padding: 8px 10px;
}

.transferDegreeBox.compact .infoRow {
  font-size: 0.75rem;
  margin-bottom: 6px;
}

.transferDegreeBox.compact .label {
  min-width: 70px;
  font-size: 0.7rem;
}

.transferDegreeBox.compact .value {
  font-size: 0.7rem;
}

.transferDegreeBox.compact .transferButton {
  font-size: 0.7rem;
  padding: 6px 10px;
}

.transferDegreeBox.compact .transferDescription {
  font-size: 0.65rem;
}
