/* متغيرات الألوان الجديدة */
:root {
  --primary-dark: #23494A;
  --primary-light: #BFFFC7;
  --text-dark: #2d3436;
  --text-light: #636e72;
  --gradient-bg: linear-gradient(135deg, #23494A 0%, #BFFFC7 100%);
  --gradient-light: linear-gradient(135deg, rgba(35, 73, 74, 0.1) 0%, rgba(191, 255, 199, 0.1) 100%);
}

.modalContent {
  direction: rtl;
  text-align: right;
  padding: 0;
  background: var(--gradient-bg);
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: none;
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  position: relative;
  box-shadow: 0 8px 32px rgba(35, 73, 74, 0.3);
  font-family: Arial, sans-serif;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Header بالألوان الجديدة */
.notesHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin: 0;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 15px 15px 0 0;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notesHeaderLeft {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notesHeaderIcon {
  color: white;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.notesHeaderTitle {
  font-size: 1.5rem;
  color: white;
  margin: 0;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.expandArrow {
  color: white;
  font-size: 1.2rem;
  transition: transform 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.expandArrow.expanded {
  transform: rotate(180deg);
}

.modalTitle {
  margin-top: 0;
  color: #5a4d41;
  text-align: right;
  font-size: 16px;
  font-weight: 500;
}

.buttonRow {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 25px;
}

.styledButton {
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  background: var(--gradient-light);
  color: var(--primary-dark);
  border: 1.5px solid rgba(35, 73, 74, 0.3);
  font-family: inherit;
  white-space: nowrap;
  transition: all 0.3s ease;
  font-weight: 500;
}

.styledButton:hover {
  background: var(--gradient-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(35, 73, 74, 0.3);
  color: white;
}

/* المحتوى الداخلي بالألوان الجديدة */
.notesContent {
  padding: 0;
  background: rgba(255, 255, 255, 0.95);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: visible;
}

.notesInnerContent {
  padding: 15px;
  flex: 1;
}

.textareaSection {
  margin-bottom: 15px;
}

.typeSection {
  margin-bottom: 15px;
}

.textareaTitle {
  font-size: 16px;
  color: var(--primary-dark);
  text-align: right;
  margin-bottom: 5px;
  font-weight: 600;
}

.textareaContainer {
  display: flex;
  justify-content: center;
}

.styledInput {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 10px;
  color: var(--primary-dark);
  text-align: center;
  font-family: inherit;
  font-size: 14px;
  border: 1.5px solid rgba(35, 73, 74, 0.3);
  resize: none;
  direction: rtl;
  width: 100%;
  box-sizing: border-box;
  min-height: 60px;
}

.styledInput:focus {
  outline: none;
}

.styledInput:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.buttonGroup {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.buttonGroup button {
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  background: var(--gradient-light);
  color: var(--primary-dark);
  border: 1px solid rgba(35, 73, 74, 0.3);
  flex: 1;
  margin: 0 5px;
  transition: all 0.3s ease;
}

.buttonGroup button:hover {
  background: var(--gradient-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(35, 73, 74, 0.4);
  color: white;
}

.buttonGroup button:disabled {
  background: #e0e0e0;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.buttonGroup button:disabled:hover {
  background: #e0e0e0;
  transform: none;
  box-shadow: none;
}

.activeButton {
  background: var(--gradient-bg);
  color: white;
  border-color: rgba(191, 255, 199, 0.5);
  box-shadow: 0 2px 8px rgba(35, 73, 74, 0.3);
}

/* ==== الملاحظات المحفوظة المعدلة === */
.savedNotesList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
  margin-top: 12px;
  padding: 0;
  list-style: none;
}

.savedNoteItem {
  background: var(--gradient-light);
  border: 1.5px solid rgba(35, 73, 74, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--primary-dark);
  white-space: nowrap;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.savedNoteItem:hover {
  background: rgba(191, 255, 199, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(191, 255, 199, 0.3);
}

/* زر الحذف */
.deleteButton {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
}

.savedNoteItem.showDelete .deleteButton {
  opacity: 1;
  transform: scale(1);
}

.deleteButton:hover {
  background: #c0392b;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.6);
}

.noteRow {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
}

.noteType {
  font-weight: bold;
  color: var(--primary-dark);
  font-size: 14px;
}

.noteContent {
  color: var(--primary-dark);
  font-size: 14px;
  flex: 1;
  text-align: right;
}

/* زر عرض المزيد ملتصق بأسفل الصفحة وبدون حدود واضحة */
.showMoreButtonSticky {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  max-width: 100vw;
  z-index: 100;
  background: transparent;
  border: none;
  box-shadow: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
}

.showMoreButtonSticky button,
.styledButton.showMoreButtonSticky {
  width: 100vw;
  max-width: 100vw;
  border: none;
  background: transparent;
  color: #5a4d41;
  font-size: 1.1rem;
  font-weight: 500;
  border-radius: 0;
  box-shadow: none;
  padding: 18px 0 22px 0;
  margin: 0;
  outline: none;
  transition: background 0.2s;
}

.showMoreButtonSticky button:hover,
.styledButton.showMoreButtonSticky:hover {
  background: rgba(255, 248, 242, 0.7);
  color: #5a4d41;
}

/* زر عرض المزيد/أقل مثل الكروت ولازق على الطرف */
.expandToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-dark);
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  margin-top: auto;
  border-radius: 0 0 15px 15px;
  font-weight: 500;
  user-select: none;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  flex-shrink: 0;
}

.expandToggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(191, 255, 199, 0.3), transparent);
  transition: left 0.6s ease;
}

.expandToggle:hover::before {
  left: 100%;
}

.expandToggle:hover {
  background: rgba(255, 255, 255, 1);
  color: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(191, 255, 199, 0.3);
}

/* انيميشن الأيقونة */
.expandToggle svg {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.expandToggle:hover svg {
  transform: scale(1.1);
}

@media (max-width: 600px) {
  .showMoreButtonSticky,
  .showMoreButtonSticky button,
  .styledButton.showMoreButtonSticky {
    font-size: 1rem;
    padding: 14px 0 18px 0;
  }
}

@media (max-width: 768px) {
  .modalContent {
    max-width: 98vw;
    min-width: 320px;
    width: 100%;
    margin: 0 auto;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 18px 8px 18px 8px;
  }
}
