import { useState, useEffect } from 'react';
import { FaSave, FaTimes } from 'react-icons/fa';
import styles from "./AddDeferralAction.module.css";
import { db } from '../../config/firebaseConfig';
import { collection, getDocs, query, where } from 'firebase/firestore';

const AddDeferral = ({
  currentUser, onSave, onClose, isUnderConsideration,
}) => {
  const [reportDate, setReportDate] = useState('');
  const [selectedReasons, setSelectedReasons] = useState([]);
  const [deferralDescription, setDeferralDescription] = useState(''); // حقل وصف التأجيل الجديد
  const [error, setError] = useState(null);
  const [templates, setTemplates] = useState([]);

  useEffect(() => {
    const fetchTemplates = async () => {
      if (!currentUser || !currentUser.uid) {
        setError('لا يوجد مستخدم مسجل الدخول. يرجى تسجيل الدخول لعرض القوالب.');
        return;
      }

      try {
        const templatesRef = collection(db, 'deferralTemplates');
        const q = query(templatesRef, where('userId', '==', currentUser.uid));
        const querySnapshot = await getDocs(q);
        const userTemplates = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        setTemplates(userTemplates);
        console.log('Loaded Templates:', userTemplates);
      } catch (e) {
        console.error('Error fetching templates:', e);
        setError('خطأ في جلب القوالب: ' + (e.message || 'غير معروف'));
      }
    };
    fetchTemplates();
  }, [currentUser]);

  const toggleReasonSelection = (reason) => {
    setSelectedReasons((prev) =>
      prev.includes(reason)
        ? prev.filter((r) => r !== reason)
        : [...prev, reason]
    );
  };

  const handleSave = async () => {
    try {
      await onSave(reportDate, selectedReasons, deferralDescription, setError);
    } catch (err) {
      console.error('Error in handleSave:', err);
      setError('خطأ أثناء حفظ التأجيل: ' + (err.message || 'غير معروف'));
    }
  };

  if (isUnderConsideration) {
    return (
      <div style={{ color: 'red', textAlign: 'center', padding: '20px' }}>
        لا يمكن إضافة تأجيلة لأن حالة القضية "قيد النظر".
      </div>
    );
  }

  return (
    <div className={styles.addReportForm}>
      {error && <div style={{ color: 'red', marginBottom: '15px' }}>{error}</div>}
      <div className={styles.dateReasonSection}>
        <div className={styles.dateField}>
          <label>تاريخ التأجيلة:</label>
          <input
            type="date"
            value={reportDate}
            onChange={(e) => setReportDate(e.target.value)}
            className={styles.dateInput}
            min={new Date().toISOString().split('T')[0]}
          />
        </div>
        <div className={styles.reasonSection}>
          <label>أسباب التأجيلة:</label>
          <div className={styles.selectedReasons}>
            {selectedReasons.length > 0 ? (
              selectedReasons.join('، ')
            ) : (
              <span className={styles.noSelection}>
                لم يتم اختيار أسباب
              </span>
            )}
          </div>
          <div className={styles.reasonButtons}>
            {templates.map((template) => (
              <button
                key={template.category || template.reason}
                className={`${styles.reasonButton} ${
                  selectedReasons.includes(template.category || template.reason) ? styles.selected : ''
                }`}
                onClick={() => toggleReasonSelection(template.category || template.reason)}
                type="button"
              >
                {template.category || template.reason}
              </button>
            ))}
          </div>
        </div>

        {/* حقل وصف التأجيل الجديد */}
        <div className={styles.descriptionSection}>
          <label className={styles.optionalLabel}>
            وصف التأجيل
            <span className={styles.optional}>(اختياري)</span>
          </label>
          <textarea
            value={deferralDescription}
            onChange={(e) => setDeferralDescription(e.target.value)}
            className={styles.descriptionTextarea}
            placeholder="أدخل تفاصيل إضافية عن التأجيل..."
            rows={3}
          />
        </div>
      </div>

      <div className={styles.reportFormButtons}>
        <button
          onClick={handleSave}
          className={styles.saveButton}
        >
          <FaSave className={styles.buttonIcon} />
          <span>حفظ الإضافة</span>
        </button>        <button
          onClick={onClose}
          className={styles.cancelButton}
        >
          <FaTimes className={styles.buttonIcon} />
          <span>إلغاء</span>
        </button>
      </div>
    </div>
  );
};

export default AddDeferral;