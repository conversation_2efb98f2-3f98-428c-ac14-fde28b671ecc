import React, { useState } from 'react';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import {
  FaUser, FaInfoCircle, FaPen, FaSave, FaTimes, FaPlus,
  FaIdCard, FaBuilding, FaCheck, FaTimes as FaTimesCircle,
  FaChevronDown, FaChevronUp, FaExchangeAlt, FaFileAlt,
  FaGavel, FaUsers
} from 'react-icons/fa';
import { caseCategoriesByDegree, courtLocations, caseDegrees } from "../../utils/CaseFilters";
import { getActiveAccount, updateCase } from '../../services/StorageService';
import styles from "./CaseDetails.module.css";
import CaseArchiveView from './CaseArchiveView'; // استيراد مكون الأرشيف الزمني الأصلي

// دالة مساعدة لتحويل التاريخ بشكل موحد
function parseDate(dateInput) {
  if (!dateInput) return null;
  // إذا كان التاريخ بصيغة YYYY-MM-DD
  if (typeof dateInput === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
    return dateInput;
  }
  // إذا كان التاريخ بتنسيق DD/MM/YYYY
  if (typeof dateInput === 'string' && dateInput.includes('/')) {
    const parts = dateInput.split('/');
    if (parts.length === 3) {
      const day = parts[0].padStart(2, '0');
      const month = parts[1].padStart(2, '0');
      const year = parts[2];
      return `${year}-${month}-${day}`;
    }
  }
  // محاولة تحويل مباشر
  const date = new Date(dateInput);
  if (!isNaN(date.getTime())) {
    return date.toISOString().split('T')[0];
  }
  return null;
}

const CaseInfoGroups = ({ caseData, currentUser, onCaseDataUpdate }) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [editingField, setEditingField] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [customValue, setCustomValue] = useState('');
  const [error, setError] = useState('');
  const [saving, setSaving] = useState(false);

  // حالة لإظهار حقول رقم القضية وسنة القضية عند تعديل الرقم الكامل
  const [showCaseNumberFields, setShowCaseNumberFields] = useState(false);
  const [caseNumberValue, setCaseNumberValue] = useState('');
  const [caseYearValue, setCaseYearValue] = useState('');

  // حالة تحويل الدرجة
  const [showTransferConfirmation, setShowTransferConfirmation] = useState(false);

  // حالة أزرار الإحالة
  const [showCourtReferralModal, setShowCourtReferralModal] = useState(false);

  // حالة تحويل حالة الملف
  const [showStatusTransferConfirmation, setShowStatusTransferConfirmation] = useState(false);
  const [selectedNewStatus, setSelectedNewStatus] = useState('');
  const [lawsuitDate, setLawsuitDate] = useState('');
  const [reportDate, setReportDate] = useState('');

  // حالة توسيع البطاقات (بدون الأرشيف، لأنه مكون منفصل الآن)
  const [expandedCards, setExpandedCards] = useState({
    parties: false, // جميع البطاقات مقفولة افتراضياً
    identification: false,
    location: false,
  });

  const caseCategoryOptions = caseData?.caseDegree ? caseCategoriesByDegree[caseData.caseDegree] || [] : [];

  // معلومات الأطراف والوصف
  const partiesInfoItems = [
    { label: 'اسم الموكل', value: caseData.clientName, field: 'clientName', required: true, type: 'text', primary: true },
    { label: 'اسم الخصم', value: caseData.opponentName || 'غير محدد', field: 'opponentName', required: false, type: 'text', primary: true },
    { label: 'الوصف العام للقضية', value: caseData.caseDescription || 'غير محدد', field: 'caseDescription', required: false, type: 'textarea', primary: false },
  ];

  // معلومات الترقيم والتصنيف
  const identificationItems = [
    ...(caseData.caseStatus !== 'قيد النظر' ? [
      {
        label: caseData.caseStatus === 'محضر' ? 'رقم المحضر الكامل' : 'رقم القضية الكامل',
        value: caseData.fullCaseNumber || '—',
        field: 'fullCaseNumber',
        required: true,
        readOnly: false, // جعل الحقل قابل للتعديل
        type: 'text',
        primary: true,
        isFullCaseNumber: true // علامة خاصة لتمييز هذا الحقل
      }
    ] : []),
    // إضافة درجة الدعوى ونوع الدعوى للدعاوى القضائية
    ...(caseData.caseStatus === 'دعوى قضائية' ? [
      { label: 'درجة الدعوى', value: caseData.caseDegree || 'غير محدد', field: 'caseDegree', required: true, type: 'select', options: caseDegrees, primary: true },
      { label: 'نوع الدعوى', value: caseData.caseCategory || 'غير محدد', field: 'caseCategory', required: true, type: 'select', options: caseCategoryOptions, primary: true },
    ] : []),
    // إخفاء حقلي رقم القضية وسنة القضية من الواجهة الأساسية
    // ...(caseData.caseStatus === 'دعوى قضائية' ? [
    //   { label: 'رقم القضية لدى المحكمة', value: caseData.caseNumber || 'غير محدد', field: 'caseNumber', required: true, type: 'text', primary: true },
    //   { label: 'سنة القضية', value: caseData.caseYear || 'غير محدد', field: 'caseYear', required: true, type: 'text', primary: false },
    // ] : []),
    // إخفاء حقل رقم المحضر المنفصل لأنه موجود في رقم المحضر الكامل
    // ...(caseData.caseStatus === 'محضر' ? [
    //   { label: 'رقم المحضر', value: caseData.reportNumber || '—', field: 'reportNumber', required: true, type: 'text', primary: true },
    // ] : []),
  ];

  // معلومات الجهة والمكان
  const locationInfoItems = caseData.caseStatus === 'قيد النظر' ? [
    { label: 'مكان الجهة المختصة', value: caseData.reportLocation || 'غير محدد', field: 'reportLocation', required: true, type: 'text', primary: true },
  ] : caseData.caseStatus === 'محضر' ? [
    { label: 'مكان الجهة المختصة', value: caseData.reportLocation || 'غير محدد', field: 'reportLocation', required: true, type: 'text', primary: true },
  ] : caseData.caseStatus === 'دعوى قضائية' ? [
    { label: 'رقم الدائرة', value: caseData.circleNumber || 'غير محدد', field: 'circleNumber', required: false, type: 'text', primary: true },
    { label: 'مكان المحكمة', value: caseData.courtLocation || 'غير محدد', field: 'courtLocation', required: true, type: 'text', primary: true, isCourtReferral: true },
  ] : [];



  // إزالة منطق الأرشيف الزمني المخصص من هنا

  const getGroupTitle = (groupType) => {
    switch (groupType) {
      case 'parties':
        // عرض حالة القضية كعنوان
        if (caseData.caseStatus && caseData.caseStatus !== 'غير محدد') {
          return caseData.caseStatus;
        }
        return 'الأطراف والوصف';
      case 'identification':
        if (caseData.caseStatus === 'محضر') return 'ترقيم المحضر';
        if (caseData.caseStatus === 'دعوى قضائية') {
          // عرض درجة الدعوى كعنوان إذا كانت متوفرة
          if (caseData.caseDegree && caseData.caseDegree !== 'غير محدد') {
            return caseData.caseDegree;
          }
          return 'ترقيم القضية';
        }
        return 'تصنيف الملف';
      case 'location':
        if (caseData.caseStatus === 'قيد النظر') {
          // عرض اسم الجهة المختصة كعنوان إذا كانت متوفرة
          if (caseData.reportLocation && caseData.reportLocation !== 'غير محدد') {
            return caseData.reportLocation;
          }
          return 'الجهة المختصة';
        }
        if (caseData.caseStatus === 'محضر') {
          // عرض اسم الجهة المختصة كعنوان إذا كانت متوفرة
          if (caseData.reportLocation && caseData.reportLocation !== 'غير محدد') {
            return caseData.reportLocation;
          }
          return 'الجهة المختصة';
        }
        if (caseData.caseStatus === 'دعوى قضائية') {
          // عرض اسم المحكمة كعنوان إذا كانت متوفرة
          if (caseData.courtLocation && caseData.courtLocation !== 'غير محدد') {
            return caseData.courtLocation;
          }
          return 'المحكمة المختصة';
        }
        return 'معلومات المكان';
      // لا نحتاج عنوان للأرشيف هنا لأنه سيأتي من مكونه الخاص
      default: return groupType;
    }
  };

  const getGroupIcon = (groupType) => {
    switch (groupType) {
      case 'parties': return <FaUser />;
      case 'identification': return <FaIdCard />;
      case 'location': return <FaBuilding />;
      // لا نحتاج أيقونة للأرشيف هنا
      default: return <FaInfoCircle />;
    }
  };

  const getCardColor = (groupType) => {
    switch (groupType) {
      case 'parties': return styles.partiesCard;
      case 'identification': return styles.identificationCard;
      case 'location': return styles.locationCard;
      // لا نحتاج لون للأرشيف هنا، سيتم تنسيقه بشكل منفصل
      default: return '';
    }
  };

  // مجموعات البيانات الأساسية (بدون الأرشيف)
  const caseGroups = [
    { title: getGroupTitle('parties'), icon: getGroupIcon('parties'), items: partiesInfoItems, type: 'parties' },
    { title: getGroupTitle('identification'), icon: getGroupIcon('identification'), items: identificationItems, type: 'identification' },
    { title: getGroupTitle('location'), icon: getGroupIcon('location'), items: locationInfoItems, type: 'location' },
  ].filter(group => group.items.length > 0);

  const toggleCardExpand = (cardType) => {
    setExpandedCards(prev => ({ ...prev, [cardType]: !prev[cardType] }));
  };

  const mutation = useMutation({
    mutationFn: async ({ caseId, updateData }) => {
      await updateCase(currentUser.uid, caseId, updateData);
    },
    onSuccess: (_, { caseId, updateData }) => {
      queryClient.setQueryData(['case', caseId], (oldData) => ({ ...oldData, ...updateData }));
      if (onCaseDataUpdate) { onCaseDataUpdate(updateData); }
      setEditingField(null); setEditValue(''); setCustomValue(''); setError('');
      const activeAccount = getActiveAccount();
      alert(`تم حفظ التعديل بنجاح في الحساب ${activeAccount === 'online' ? 'الأونلاين' : 'المحلي'}!`);
    },
    onError: (error) => {
      console.error('Error saving field:', error);
      setError('حدث خطأ أثناء حفظ التعديل: ' + error.message);
    },
    onSettled: () => { setSaving(false); },
  });

  const handleEditClick = (field, currentValue) => {
    // إذا كان الحقل هو درجة الدعوى، فتح نظام تحويل الدرجة بدلاً من التعديل العادي
    if (field.field === 'caseDegree' && canTransferDegree()) {
      handleTransferDegree();
      return;
    }

    setEditingField({ ...field, groupType: caseGroups.find(group => group.items.some(item => item.field === field.field))?.type });
    setEditValue(currentValue === 'غير محدد' || currentValue === '—' ? '' : currentValue || '');
    setCustomValue(''); setError('');

    // إذا كان الحقل هو رقم القضية الكامل، قم بإظهار حقول رقم القضية وسنة القضية
    if (field.isFullCaseNumber && caseData.caseStatus === 'دعوى قضائية') {
      setShowCaseNumberFields(true);
      setCaseNumberValue(caseData.caseNumber || '');
      setCaseYearValue(caseData.caseYear || '');
    }
    // إذا كان الحقل هو رقم المحضر الكامل، قم بإظهار حقول رقم المحضر وسنة المحضر
    else if (field.isFullCaseNumber && caseData.caseStatus === 'محضر') {
      setShowCaseNumberFields(true);
      setCaseNumberValue(caseData.reportNumber || '');
      setCaseYearValue(caseData.caseYear || '');
    }
  };

  const handleSaveEdit = () => {
    if (!editingField || !caseData || !currentUser) { setError("لا يمكن حفظ التعديل حالياً. يرجى تحديث الصفحة."); return; }

    // منطق خاص لحقل رقم القضية الكامل
    if (editingField.isFullCaseNumber && caseData.caseStatus === 'دعوى قضائية') {
      // التحقق من صحة البيانات
      if (!caseNumberValue.trim()) { setError('رقم القضية مطلوب'); return; }
      if (!caseYearValue.trim()) { setError('السنة القضائية مطلوبة'); return; }
      if (!/^\d+$/.test(caseNumberValue.trim())) { setError('رقم القضية يجب أن يحتوي على أرقام فقط'); return; }
      if (!/^\d{4}$/.test(caseYearValue.trim())) { setError('السنة القضائية يجب أن تتكون من 4 أرقام فقط'); return; }

      setError(''); setSaving(true);
      const fullCaseNumber = `${caseNumberValue.trim()}/${caseYearValue.trim()}`;
      const updateData = {
        fullCaseNumber: fullCaseNumber,
        caseNumber: caseNumberValue.trim(),
        caseYear: caseYearValue.trim()
      };

      mutation.mutate({ caseId: caseData.id, updateData });
      setShowCaseNumberFields(false);
      return;
    }

    // منطق خاص لحقل رقم المحضر الكامل
    if (editingField.isFullCaseNumber && caseData.caseStatus === 'محضر') {
      // التحقق من صحة البيانات
      if (!caseNumberValue.trim()) { setError('رقم المحضر مطلوب'); return; }
      if (!caseYearValue.trim()) { setError('سنة المحضر مطلوبة'); return; }
      if (!/^\d+$/.test(caseNumberValue.trim())) { setError('رقم المحضر يجب أن يحتوي على أرقام فقط'); return; }
      if (!/^\d{4}$/.test(caseYearValue.trim())) { setError('سنة المحضر يجب أن تتكون من 4 أرقام فقط'); return; }

      setError(''); setSaving(true);
      const fullCaseNumber = `${caseNumberValue.trim()}/${caseYearValue.trim()}`;
      const updateData = {
        fullCaseNumber: fullCaseNumber,
        reportNumber: caseNumberValue.trim(),
        caseYear: caseYearValue.trim()
      };

      mutation.mutate({ caseId: caseData.id, updateData });
      setShowCaseNumberFields(false);
      return;
    }

    if (editingField.required && !editValue.trim() && editingField.type !== 'select') { setError(`${editingField.label} مطلوب`); return; }

    let finalValue = editValue.trim();
    if (editingField.type === 'select') {
      if (editValue === 'أخرى') {
        if (!customValue.trim()) { setError('يرجى إدخال قيمة مخصصة لـ "أخرى"'); return; }
        finalValue = customValue.trim();
      }
      if (editingField.required && !finalValue) { setError(`${editingField.label} مطلوب`); return; }
      if (editingField.field === 'referredToExpert') { finalValue = finalValue === 'نعم'; }
    }

    if (editingField.field === 'reportNumber' && finalValue && !/^\d+$/.test(finalValue)) { setError('رقم المحضر يجب أن يحتوي على أرقام فقط'); return; }
    if (editingField.field === 'caseNumber' && finalValue && !/^\d+$/.test(finalValue)) { setError('رقم القضية يجب أن يحتوي على أرقام فقط'); return; }
    if (editingField.field === 'caseYear' && finalValue && !/^\d{4}$/.test(finalValue)) { setError('سنة القضية يجب أن تتكون من 4 أرقام فقط'); return; }

    setError(''); setSaving(true);
    const updateData = { [editingField.field]: finalValue };

    // Logic for updating related fields based on caseStatus, caseNumber, caseYear, etc.
    if (editingField.field === 'caseStatus') {
      const newStatus = finalValue;
      let fieldsToClear = {};
      if (newStatus === 'محضر' || newStatus === 'قيد النظر') {
        fieldsToClear = { caseNumber: '', caseYear: '', circleNumber: '', caseDegree: '', caseCategory: '', courtLocation: '', fullCaseNumber: '' };
      } else if (newStatus === 'دعوى قضائية') {
        fieldsToClear = { reportLocation: '', reportNumber: '' };
        const currentCaseNum = updateData.hasOwnProperty('caseNumber') ? updateData.caseNumber : caseData.caseNumber;
        const currentCaseYr = updateData.hasOwnProperty('caseYear') ? updateData.caseYear : caseData.caseYear;
        if (currentCaseNum && currentCaseYr && currentCaseNum !== 'غير محدد' && currentCaseYr !== 'غير محدد') {
          updateData.fullCaseNumber = `${currentCaseNum}/${currentCaseYr}`;
        } else { updateData.fullCaseNumber = ''; }
        if (!caseData.caseYear || caseData.caseYear === 'غير محدد') { updateData.caseYear = new Date().getFullYear().toString(); }
      }
      Object.assign(updateData, fieldsToClear);
    } else if (editingField.field === 'reportNumber' && caseData.caseStatus === 'محضر') {
      updateData.fullCaseNumber = finalValue;
    } else if ((editingField.field === 'caseNumber' || editingField.field === 'caseYear') && caseData.caseStatus === 'دعوى قضائية') {
      const updatedCaseNumber = editingField.field === 'caseNumber' ? finalValue : caseData.caseNumber;
      const updatedCaseYear = editingField.field === 'caseYear' ? finalValue : caseData.caseYear;
      if (updatedCaseNumber && updatedCaseYear && updatedCaseNumber !== 'غير محدد' && updatedCaseYear !== 'غير محدد') {
        updateData.fullCaseNumber = `${updatedCaseNumber}/${updatedCaseYear}`;
      } else { updateData.fullCaseNumber = ''; }
    }
    if (editingField.field === 'caseDegree' && caseData.caseStatus === 'دعوى قضائية') {
      updateData.caseCategory = '';
    }

    mutation.mutate({ caseId: caseData.id, updateData });
  };

  const handleCancelEdit = () => {
    setEditingField(null); setEditValue(''); setCustomValue(''); setError('');
    setShowCaseNumberFields(false); // إخفاء حقول رقم القضية عند الإلغاء
    setCaseNumberValue(''); setCaseYearValue('');
  };

  // دوال تحويل الدرجة
  const handleTransferDegree = () => {
    setShowTransferConfirmation(true);
  };

  const handleTransferConfirm = async () => {
    try {
      // إخفاء القضية الحالية
      const updatedCaseData = {
        ...caseData,
        isHidden: true,
        hiddenAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await updateCase(currentUser.uid, caseData.id, updatedCaseData);

      // تحضير البيانات للنقل
      const transferData = {
        clientName: caseData.clientName,
        caseDescription: caseData.caseDescription,
        opponentName: caseData.opponentName,
        caseCategory: caseData.caseCategory,
        courtLocation: caseData.courtLocation,
        originalCaseId: caseData.id,
        originalCaseDegree: caseData.caseDegree,
        originalCaseNumber: caseData.fullCaseNumber,
        originalCourtLocation: caseData.courtLocation,
        originalCaseDate: caseData.caseDate,
        // حفظ جميع البيانات الأساسية للمقارنة لاحقاً
        originalClientName: caseData.clientName,
        originalOpponentName: caseData.opponentName,
        originalCaseDescription: caseData.caseDescription,
        originalCaseCategory: caseData.caseCategory,
        originalCircleNumber: caseData.circleNumber
      };

      // حفظ البيانات في localStorage للنقل
      localStorage.setItem('transferData', JSON.stringify(transferData));

      // الانتقال لصفحة إضافة قضية جديدة
      navigate('/cases');
    } catch (error) {
      alert('خطأ في تحويل الدرجة: ' + error.message);
    }
  };

  const handleTransferCancel = () => {
    setShowTransferConfirmation(false);
  };

  // دوال تحويل حالة الملف
  const handleStatusTransfer = () => {
    setShowStatusTransferConfirmation(true);
    setSelectedNewStatus('');
    setLawsuitDate('');
    setReportDate('');
  };

  const handleStatusSelection = (newStatus) => {
    setSelectedNewStatus(newStatus);
  };

  const handleStatusTransferConfirm = async () => {
    try {
      // التحقق من البيانات المطلوبة
      if (!selectedNewStatus) {
        alert('يرجى اختيار الحالة الجديدة');
        return;
      }

      if (selectedNewStatus === 'دعوى قضائية' && !lawsuitDate) {
        alert('يرجى إدخال تاريخ رفع الدعوى');
        return;
      }

      if (selectedNewStatus === 'محضر' && !reportDate) {
        alert('يرجى إدخال تاريخ كتابة المحضر');
        return;
      }

      // إعداد البيانات المحدثة
      let updatedCaseData = {
        ...caseData,
        caseStatus: selectedNewStatus,
        updatedAt: new Date().toISOString()
      };

      // تحديث البيانات حسب الحالة الجديدة
      if (selectedNewStatus === 'محضر') {
        updatedCaseData = {
          ...updatedCaseData,
          fullCaseNumber: `محضر-${Date.now()}`,
          caseDate: reportDate,
          // مسح بيانات الدعوى القضائية
          caseNumber: null,
          caseYear: null,
          circleNumber: null,
          caseDegree: null,
          caseCategory: null,
          courtLocation: null
        };
      } else if (selectedNewStatus === 'دعوى قضائية') {
        updatedCaseData = {
          ...updatedCaseData,
          fullCaseNumber: `دعوى-${Date.now()}`,
          caseDate: lawsuitDate,
          // الاحتفاظ ببيانات المحضر السابق (لا يتم مسحها)
          // reportNumber: يبقى كما هو
          // reportLocation: يبقى كما هو
          // caseYear: يبقى كما هو (سنة المحضر)
        };
      } else if (selectedNewStatus === 'قيد النظر') {
        updatedCaseData = {
          ...updatedCaseData,
          fullCaseNumber: `قيد النظر-${Date.now()}`,
          reportNumber: null,
          reportLocation: null,
          caseNumber: null,
          caseYear: null,
          circleNumber: null,
          caseDegree: null,
          caseCategory: null,
          courtLocation: null,
          caseDate: null
        };
      }

      // إنشاء أرشيف أولي إذا لم يكن موجوداً (للقضايا القديمة)
      let initialHistory = [...(caseData.history || [])];

      // إذا لم يكن هناك أرشيف، أنشئ إدخال واحد فقط حسب نوع القضية
      if (initialHistory.length === 0) {
        if (caseData.caseStatus === 'محضر' && caseData.caseDate) {
          // للمحضر: حدث واحد فقط بتاريخ المحضر
          const reportEntry = {
            type: 'report_created',
            timestamp: new Date(caseData.caseDate).toISOString(),
            action: `تم تحرير المحضر بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
            date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
            reportDate: caseData.caseDate
          };
          initialHistory.push(reportEntry);
        } else if (caseData.caseStatus === 'دعوى قضائية' && caseData.caseDate) {
          // للدعوى: حدث واحد فقط بتاريخ رفع الدعوى
          const lawsuitEntry = {
            type: 'lawsuit_created',
            timestamp: new Date(caseData.caseDate).toISOString(),
            action: `تم رفع دعوى قضائية بتاريخ ${new Date(caseData.caseDate).toLocaleDateString('ar-EG')}`,
            date: new Date(caseData.caseDate).toLocaleDateString('ar-EG'),
            lawsuitDate: caseData.caseDate
          };
          initialHistory.push(lawsuitEntry);
        } else {
          // لقيد النظر أو القضايا بدون تاريخ: حدث إنشاء عادي
          const creationEntry = {
            type: 'case_created',
            timestamp: caseData.createdAt || new Date().toISOString(),
            action: `تم إنشاء ${caseData.caseStatus}`,
            date: new Date(caseData.createdAt || new Date()).toLocaleDateString('ar-EG')
          };
          initialHistory.push(creationEntry);
        }
      }

      // إضافة إدخال في الأرشيف الزمني
      const currentDate = new Date().toLocaleDateString('ar-EG');
      const statusTransferEntry = {
        type: 'status_transfer',
        timestamp: new Date().toISOString(),
        action: `تم تحويل حالة الملف من "${caseData.caseStatus}" إلى "${selectedNewStatus}"`,
        date: currentDate,
        oldStatus: caseData.caseStatus,
        newStatus: selectedNewStatus
      };

      const updatedHistory = [...initialHistory, statusTransferEntry];
      updatedCaseData.history = updatedHistory;

      // تسجيل للتأكد من البيانات
      console.log('الأحداث الأصلية:', caseData.history?.length || 0);
      console.log('الأحداث الأولية (مع الإنشاء):', initialHistory.length);
      console.log('الأحداث النهائية (مع التحويل):', updatedHistory.length);
      console.log('البيانات المحدثة:', updatedCaseData);

      // حفظ البيانات
      await updateCase(currentUser.uid, caseData.id, updatedCaseData);

      // مسح التخزين المؤقت
      const CACHE_KEY = `case_${caseData.id}`;
      localStorage.removeItem(CACHE_KEY);

      // تحديث بيانات القضية في المكون الأب
      if (onCaseDataUpdate) {
        onCaseDataUpdate(updatedCaseData);
      }

      alert('تم تحويل حالة الملف بنجاح');
      setShowStatusTransferConfirmation(false);
      setSelectedNewStatus('');
      setLawsuitDate('');
      setReportDate('');
    } catch (error) {
      alert('خطأ في تحويل الحالة: ' + error.message);
    }
  };

  const handleStatusTransferCancel = () => {
    setShowStatusTransferConfirmation(false);
    setSelectedNewStatus('');
    setLawsuitDate('');
    setReportDate('');
  };

  // حالات نظام الإحالة المبسط
  const [showSimpleCourtReferral, setShowSimpleCourtReferral] = useState(false);
  const [showReferralConfirmation, setShowReferralConfirmation] = useState(false);
  const [referralJudgmentDate, setReferralJudgmentDate] = useState('');
  const [newCourtLocation, setNewCourtLocation] = useState('');
  const [referralDateError, setReferralDateError] = useState('');

  // دوال معالجة أزرار الإحالة
  const handleCourtReferral = () => {
    // فتح سؤال التأكيد أولاً
    setShowReferralConfirmation(true);
    setReferralJudgmentDate('');
    setNewCourtLocation('');
    setReferralDateError('');
  };

  // دالة تأكيد الإحالة (الانتقال للنموذج)
  const handleReferralConfirmYes = () => {
    setShowReferralConfirmation(false);
    setShowSimpleCourtReferral(true);
  };

  // دالة إلغاء الإحالة
  const handleReferralConfirmNo = () => {
    setShowReferralConfirmation(false);
  };

  // دالة للحصول على تواريخ الأحكام من Timeline Archive
  const getJudgmentDatesFromTimeline = (caseData) => {
    const judgmentDates = [];

    if (!caseData) return judgmentDates;

    // البحث في timeline المخصص
    if (caseData.timeline && Array.isArray(caseData.timeline)) {
      caseData.timeline.forEach((event) => {
        if (event.description && event.date && (
          event.description.includes('حكم') ||
          event.description.includes('للحكم') ||
          event.description.includes('قرار') ||
          (event.description.includes('جلسة') && event.description.includes('للحكم'))
        )) {
          const formattedDate = parseDate(event.date);
          if (formattedDate && !judgmentDates.includes(formattedDate)) {
            judgmentDates.push(formattedDate);
          }
        }
      });
    }

    // البحث في history المكتمل
    if (caseData.history && Array.isArray(caseData.history)) {
      caseData.history.forEach((historyItem) => {
        if ((historyItem.type === 'completed_deferral' || historyItem.type === 'completed_action') &&
            historyItem.description && historyItem.date && (
              historyItem.description.includes('حكم') ||
              historyItem.description.includes('للحكم') ||
              historyItem.description.includes('قرار') ||
              (historyItem.description.includes('جلسة') && historyItem.description.includes('للحكم'))
            )) {
          const formattedDate = parseDate(historyItem.date);
          if (formattedDate && !judgmentDates.includes(formattedDate)) {
            judgmentDates.push(formattedDate);
          }
        }
      });
    }

    return judgmentDates;
  };

  // دالة حفظ الإحالة المبسطة
  const handleSimpleCourtReferralSave = async () => {
    setReferralDateError('');

    // التحقق من الحقول المطلوبة
    if (!referralJudgmentDate) {
      setReferralDateError('يرجى إدخال تاريخ الحكم');
      return;
    }

    if (!newCourtLocation) {
      setReferralDateError('يرجى اختيار المحكمة الجديدة');
      return;
    }

    // التحقق من تطابق التاريخ مع الأحكام الموجودة في Timeline
    const existingJudgmentDates = getJudgmentDatesFromTimeline(caseData);
    const inputDate = parseDate(referralJudgmentDate);

    // إذا توجد تواريخ أحكام، نتحقق من التطابق
    if (existingJudgmentDates.length > 0 && !existingJudgmentDates.includes(inputDate)) {
      const formattedDates = existingJudgmentDates.map(date => {
        // تأكد أن التاريخ صالح قبل تحويله
        const d = new Date(date);
        return !isNaN(d.getTime()) ? d.toLocaleDateString('ar-EG') : date;
      }).join('، ');
      setReferralDateError(`التاريخ المدخل لا يتطابق مع تاريخ الحكم الموجود في الأرشيف الزمني.\n\nالتواريخ المتاحة: ${formattedDates}\n\nيرجى إدخال التاريخ الصحيح للحكم.`);
      return;
    }

    try {
      // إنشاء timeline entry للإحالة
      const timelineEntry = {
        date: new Date(referralJudgmentDate).toLocaleDateString('ar-EG'),
        description: `تم إحالة القضية للمحكمة ${newCourtLocation} بتاريخ ${new Date(referralJudgmentDate).toLocaleDateString('ar-EG')}`,
        type: 'court_transfer',
        timestamp: new Date().toISOString()
      };

      // تحديث البيانات الأساسية للقضية
      const updatedCaseData = {
        ...caseData,
        courtLocation: newCourtLocation,
        timeline: [...(caseData.timeline || []), timelineEntry],
        courtReferral: {
          ...caseData?.courtReferral,
          transferDate: new Date().toISOString(),
          judgmentDate: referralJudgmentDate,
          confirmationCompleted: true,
          confirmationDate: new Date().toISOString()
        },
        updatedAt: new Date().toISOString()
      };

      // حفظ البيانات
      await updateCase(currentUser.uid, caseData.id, updatedCaseData);

      // تحديث بيانات القضية في المكون الأب
      if (onCaseDataUpdate) {
        onCaseDataUpdate(updatedCaseData);
      }

      // إغلاق النافذة وإظهار رسالة نجاح
      setShowSimpleCourtReferral(false);
      alert('تم إحالة القضية للمحكمة الجديدة وتحديث الأرشيف الزمني بنجاح');

    } catch (error) {
      setReferralDateError('خطأ في حفظ البيانات: ' + error.message);
    }
  };

  // دالة إلغاء الإحالة المبسطة
  const handleSimpleCourtReferralCancel = () => {
    setShowSimpleCourtReferral(false);
    setShowReferralConfirmation(false);
    setReferralJudgmentDate('');
    setNewCourtLocation('');
    setReferralDateError('');
  };

  // التحقق من إمكانية تحويل الدرجة
  const canTransferDegree = () => {
    return caseData?.caseStatus === 'دعوى قضائية' &&
           !caseData?.isHidden;
  };

  // التحقق من إمكانية تحويل حالة الملف
  const canTransferStatus = () => {
    return caseData?.caseStatus &&
           caseData?.caseStatus !== 'غير محدد' &&
           caseData?.caseStatus !== 'دعوى قضائية' && // منع التحويل من دعوى قضائية
           !caseData?.isHidden;
  };

  // تحديد نص السؤال حسب الحالة الحالية
  const getStatusTransferQuestion = () => {
    switch (caseData?.caseStatus) {
      case 'قيد النظر':
        return 'هل تريد عمل محضر أم رفع دعوى قضائية؟';
      case 'محضر':
        return 'هل تريد رفع دعوى قضائية؟';
      // تم إزالة حالة 'دعوى قضائية' لأنه لا يمكن التحويل منها
      default:
        return 'تحويل حالة الملف';
    }
  };

  // تحديد نص الإجراء التالي حسب درجة الدعوى
  const getNextActionText = () => {
    const currentDegree = caseData?.caseDegree;

    switch (currentDegree) {
      case 'ابتدائي':
        return 'هل تود متابعة هذه القضية بالاستئناف؟';
      case 'استئنافي':
        return 'هل تود متابعة هذه القضية بالنقض؟';
      case 'نقض':
        return 'هل تود متابعة هذه القضية بإعادة النظر؟';
      case 'أخرى':
        return 'هل تود تحويل هذه القضية لدرجة أخرى؟';
      default:
        return 'هل تود تحويل هذه القضية لدرجة أخرى؟';
    }
  };

  // تحديد نص الزر المختصر
  const getButtonText = () => {
    const currentDegree = caseData?.caseDegree;

    switch (currentDegree) {
      case 'ابتدائي':
        return 'متابعة بالاستئناف';
      case 'استئنافي':
        return 'متابعة بالنقض';
      case 'نقض':
        return 'متابعة بإعادة النظر';
      case 'أخرى':
        return 'تحويل الدرجة';
      default:
        return 'تحويل الدرجة';
    }
  };

  if (!caseData) { return <div>جاري تحميل بيانات القضية...</div>; }

  // Render function for a single data field (horizontal layout)
  const renderDataField = (item) => {
    const isEditingThisField = editingField && editingField.field === item.field;
    const hasValue = item.value && item.value !== 'غير محدد' && item.value !== '—' && item.value !== '';
    let displayValue = item.value;
    if (item.field === 'referredToExpert') {
      displayValue = item.value === true || item.value === 'نعم' ? 'نعم' : 'لا';
    }

    return (
      <div key={item.field} className={styles.dataFieldHorizontal}>
        <span className={styles.fieldLabelHorizontal}>{item.label}:</span>
        <div className={styles.fieldValueContainerHorizontal}>
          {isEditingThisField ? (
            <div className={styles.editFieldContainerHorizontal}>
              {/* منطق خاص لحقل رقم القضية الكامل */}
              {item.isFullCaseNumber && caseData.caseStatus === 'دعوى قضائية' && showCaseNumberFields ? (
                <div className={styles.fullCaseNumberEditContainer}>
                  <div className={styles.caseNumberFieldsContainer}>
                    <div className={styles.caseNumberField}>
                      <label className={styles.inlineLabel}>رقم القضية:</label>
                      <input
                        type="text"
                        value={caseNumberValue}
                        onChange={(e) => { setCaseNumberValue(e.target.value); setError(''); }}
                        className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                        placeholder="أدخل رقم القضية"
                      />
                    </div>
                    <div className={styles.caseNumberField}>
                      <label className={styles.inlineLabel}>السنة القضائية:</label>
                      <input
                        type="text"
                        value={caseYearValue}
                        onChange={(e) => { setCaseYearValue(e.target.value); setError(''); }}
                        className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                        placeholder="أدخل السنة القضائية"
                      />
                    </div>
                  </div>
                  {error && <span className={styles.errorText}>{error}</span>}
                  <div className={styles.editActionsHorizontal}>
                    <button onClick={handleSaveEdit} className={styles.saveEditButton} disabled={saving}>
                      {saving ? '...' : <FaSave />}
                    </button>
                    <button onClick={handleCancelEdit} className={styles.cancelEditButton} disabled={saving}>
                      <FaTimes />
                    </button>
                  </div>
                </div>
              ) : item.isFullCaseNumber && caseData.caseStatus === 'محضر' && showCaseNumberFields ? (
                <div className={styles.fullCaseNumberEditContainer}>
                  <div className={styles.caseNumberFieldsContainer}>
                    <div className={styles.caseNumberField}>
                      <label className={styles.inlineLabel}>رقم المحضر:</label>
                      <input
                        type="text"
                        value={caseNumberValue}
                        onChange={(e) => { setCaseNumberValue(e.target.value); setError(''); }}
                        className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                        placeholder="أدخل رقم المحضر"
                      />
                    </div>
                    <div className={styles.caseNumberField}>
                      <label className={styles.inlineLabel}>سنة المحضر:</label>
                      <input
                        type="text"
                        value={caseYearValue}
                        onChange={(e) => { setCaseYearValue(e.target.value); setError(''); }}
                        className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                        placeholder="أدخل سنة المحضر"
                      />
                    </div>
                  </div>
                  {error && <span className={styles.errorText}>{error}</span>}
                  <div className={styles.editActionsHorizontal}>
                    <button onClick={handleSaveEdit} className={styles.saveEditButton} disabled={saving}>
                      {saving ? '...' : <FaSave />}
                    </button>
                    <button onClick={handleCancelEdit} className={styles.cancelEditButton} disabled={saving}>
                      <FaTimes />
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  {(() => {
                    switch (item.type) {
                      case 'select':
                    return (
                      <>
                        <select
                          value={editValue}
                          onChange={(e) => { setEditValue(e.target.value); setError(''); }}
                          className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                        >
                          <option value="">اختر {item.label}</option>
                          {(item.options || []).map((option) => (
                            <option key={option} value={option}>{option}</option>
                          ))}
                        </select>
                        {editValue === 'أخرى' && (
                          <input
                            type="text"
                            value={customValue}
                            onChange={(e) => { setCustomValue(e.target.value); setError(''); }}
                            className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                            placeholder="أدخل قيمة مخصصة"
                          />
                        )}
                      </>
                    );
                      case 'textarea':
                        return (
                          <textarea
                            value={editValue}
                            onChange={(e) => { setEditValue(e.target.value); setError(''); }}
                            className={`${styles.editInput} ${styles.textareaInput} ${error ? styles.inputError : ''}`}
                            placeholder={`أدخل ${item.label}`}
                          />
                        );
                      case 'date':
                        return (
                          <input
                            type="date"
                            value={editValue}
                            onChange={(e) => { setEditValue(e.target.value); setError(''); }}
                            className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                          />
                        );
                      default:
                        return (
                          <input
                            type="text"
                            value={editValue}
                            onChange={(e) => { setEditValue(e.target.value); setError(''); }}
                            className={`${styles.editInput} ${error ? styles.inputError : ''}`}
                            placeholder={`أدخل ${item.label}`}
                            disabled={item.readOnly}
                          />
                        );
                    }
                  })()}
                  {error && <span className={styles.errorText}>{error}</span>}
                  <div className={styles.editActionsHorizontal}>
                    <button onClick={handleSaveEdit} className={styles.saveEditButton} disabled={saving}>
                      {saving ? '...' : <FaSave />}
                    </button>
                    <button onClick={handleCancelEdit} className={styles.cancelEditButton} disabled={saving}>
                      <FaTimes />
                    </button>
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className={styles.valueWithActionHorizontal}>
              {item.field === 'referredToExpert' ? (
                <span className={`${styles.booleanValue} ${displayValue === 'نعم' ? styles.trueValue : styles.falseValue}`}>
                  {displayValue === 'نعم' ? <FaCheck /> : <FaTimesCircle />}
                  <span>{displayValue}</span>
                </span>
              ) : (
                <span className={styles.valueTextHorizontal}>{displayValue}</span>
              )}
              {!item.readOnly && (
                <>
                  {/* زر الإحالة لمكان المحكمة */}
                  {item.isCourtReferral ? (
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleCourtReferral();
                      }}
                      className={styles.referralButtonHorizontal}
                      title="إحالة لمحكمة أخرى"
                      type="button"
                    >
                      <FaGavel />
                    </button>
                  ) : (
                    /* زر التعديل العادي للحقول الأخرى */
                    <button
                      onClick={() => handleEditClick(item, displayValue)}
                      className={styles.editIconButtonHorizontal}
                      title={hasValue ? 'تعديل' : 'إضافة'}
                    >
                      {hasValue ? <FaPen /> : <FaPlus />}
                    </button>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      {/* حاوية البطاقات - ستصبح أفقية للشاشات الكبيرة عبر CSS */}
      <div className={styles.coloredCardsContainerHorizontal}>
        {/* البطاقات الثلاث الأولى */}
        {caseGroups.map((group) => (
          <div key={group.type} className={`${styles.coloredCard} ${getCardColor(group.type)}`}>
            {/* رأس البطاقة */}
            <div className={styles.cardHeader} onClick={() => toggleCardExpand(group.type)}>
              <div className={styles.cardHeaderContent}>
                <div className={styles.cardIcon}>{group.icon}</div>
                <div className={styles.cardTitle}>{group.title}</div>
              </div>
              <div className={styles.expandButton}>
                {expandedCards[group.type] ? <FaChevronUp /> : <FaChevronDown />}
              </div>
            </div>

            {/* محتوى البطاقة الأساسي */}
            <div className={styles.cardPrimaryContent}>
              {group.items.filter(item => item.primary).map(item => renderDataField(item, group.type))}

              {/* إضافة مكونات الإحالة داخل كارت المحكمة */}
              {group.type === 'location' && caseData.caseStatus === 'دعوى قضائية' && !caseData.isHidden && (
                <>
                  {/* سؤال تأكيد الإحالة */}
                  {showReferralConfirmation && (
                    <div className={styles.transferDegreeSection}>
                      <div className={`${styles.transferDegreeBox} ${styles.locationTheme}`}>
                        <div className={styles.transferHeader}>
                          <div className={styles.transferTitleSection}>
                            <FaGavel className={styles.transferIcon} />
                            <span className={styles.transferTitle}>تأكيد الإحالة</span>
                          </div>
                          <button
                            onClick={handleSimpleCourtReferralCancel}
                            className={styles.circularCloseButton}
                            title="إلغاء"
                          >
                            <FaTimes />
                          </button>
                        </div>
                        <div className={styles.transferContent}>
                          <div className={styles.confirmationQuestion}>
                            هل تم الحكم بالإحالة لمحكمة أخرى؟
                          </div>
                          <div className={styles.circularButtonGroup}>
                            <button
                              onClick={handleReferralConfirmYes}
                              className={`${styles.circularButton} ${styles.yesButton}`}
                              title="نعم"
                            >
                              <FaCheck />
                            </button>
                            <button
                              onClick={handleReferralConfirmNo}
                              className={`${styles.circularButton} ${styles.noButton}`}
                              title="لا"
                            >
                              <FaTimes />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* نموذج الإحالة المبسط داخل الكارت */}
                  {showSimpleCourtReferral && (
                    <div className={styles.transferDegreeSection}>
                      <div className={`${styles.transferDegreeBox} ${styles.locationTheme}`}>
                        <div className={styles.transferHeader}>
                          <div className={styles.transferTitleSection}>
                            <FaGavel className={styles.transferIcon} />
                            <span className={styles.transferTitle}>إحالة لمحكمة أخرى</span>
                          </div>
                          <button
                            onClick={handleSimpleCourtReferralCancel}
                            className={styles.circularCloseButton}
                            title="إلغاء"
                          >
                            <FaTimes />
                          </button>
                        </div>
                        <div className={styles.transferContent}>
                          <div className={styles.transferInfoRow}>
                            <span className={styles.transferLabel}>تاريخ الحكم:</span>
                            <input
                              type="date"
                              value={referralJudgmentDate}
                              onChange={(e) => {
                                setReferralJudgmentDate(e.target.value);
                                setReferralDateError('');
                              }}
                              className={`${styles.inlineInput} ${referralDateError ? styles.errorInput : ''}`}
                            />
                          </div>
                          <div className={styles.transferInfoRow}>
                            <span className={styles.transferLabel}>المحكمة الجديدة:</span>
                            <select
                              value={newCourtLocation}
                              onChange={(e) => {
                                setNewCourtLocation(e.target.value);
                                setReferralDateError('');
                              }}
                              className={`${styles.inlineSelect} ${referralDateError ? styles.errorInput : ''}`}
                            >
                              <option value="">اختر المحكمة</option>
                              {courtLocations.map(court => (
                                <option key={court.name} value={court.name}>
                                  {court.name}
                                </option>
                              ))}
                            </select>
                          </div>
                          {referralDateError && (
                            <div className={styles.transferErrorMessage}>
                              {referralDateError}
                            </div>
                          )}
                          <div className={styles.circularButtonGroup}>
                            <button
                              onClick={handleSimpleCourtReferralSave}
                              className={`${styles.circularButton} ${styles.confirmButton}`}
                              disabled={!referralJudgmentDate || !newCourtLocation}
                              title="تأكيد الإحالة"
                            >
                              <FaCheck />
                            </button>
                            <button
                              onClick={handleSimpleCourtReferralCancel}
                              className={`${styles.circularButton} ${styles.cancelButton}`}
                              title="إلغاء"
                            >
                              <FaTimes />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* معلومات الخبراء */}
                  {(caseData.referredToExpert || (caseData.expertReferral && caseData.expertReferral.judgmentDate)) && (
                    <div className={styles.transferDegreeSection}>
                      <div className={`${styles.transferDegreeBox} ${styles.locationTheme}`}>
                        <div className={styles.transferHeader}>
                          <div className={styles.transferTitleSection}>
                            <FaUsers className={styles.transferIcon} />
                            <span className={styles.transferTitle}>معلومات الخبراء</span>
                          </div>
                        </div>
                        <div className={styles.transferContent}>
                          <div className={styles.transferInfoRow}>
                            <span className={styles.transferLabel}>حالة الإحالة:</span>
                            <span className={styles.transferValue}>
                              {caseData.referredToExpert ? 'محالة للخبراء' : 'غير محالة'}
                            </span>
                          </div>
                          {caseData.expertReferral && caseData.expertReferral.judgmentDate && (
                            <div className={styles.transferInfoRow}>
                              <span className={styles.transferLabel}>تاريخ الحكم بالإحالة:</span>
                              <span className={styles.transferValue}>
                                {new Date(caseData.expertReferral.judgmentDate).toLocaleDateString('ar-EG')}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* محتوى البطاقة الموسع */}
            {(group.items.filter(item => !item.primary).length > 0 || (group.type === 'identification' && canTransferDegree()) || (group.type === 'parties' && canTransferStatus())) && (
              <div className={`${styles.cardExpandedContent} ${expandedCards[group.type] ? styles.expanded : ''}`}>
                <div className={styles.expandedContentDivider}></div>
                {group.items.filter(item => !item.primary).map(item => renderDataField(item, group.type))}

                {/* إضافة مكون تحويل حالة الملف في كارت الأطراف */}
                {group.type === 'parties' && canTransferStatus() && (
                  <div className={styles.transferDegreeSection}>
                    <div className={`${styles.transferDegreeBox} ${styles.partiesTheme}`}>
                      <div className={styles.transferHeader}>
                        <div className={styles.transferTitleSection}>
                          <FaExchangeAlt className={styles.transferIcon} />
                          <span className={styles.transferTitle}>تحويل حالة الملف</span>
                        </div>
                      </div>
                      <div className={styles.transferContent}>
                        <div className={styles.transferInfoRow}>
                          <span className={styles.transferValue}>{getStatusTransferQuestion()}</span>
                        </div>
                        <button
                          className={styles.transferButton}
                          onClick={handleStatusTransfer}
                          title="تحويل حالة الملف"
                        >
                          <FaExchangeAlt />
                          تحويل الحالة
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* إضافة بيانات المحضر السابق للدعوى القضائية */}
                {group.type === 'parties' && caseData.caseStatus === 'دعوى قضائية' &&
                 (caseData.reportNumber || caseData.reportLocation) && (
                  <div className={styles.transferDegreeSection}>
                    <div className={`${styles.transferDegreeBox} ${styles.partiesTheme}`}>
                      <div className={styles.transferHeader}>
                        <div className={styles.transferTitleSection}>
                          <FaFileAlt className={styles.transferIcon} />
                          <span className={styles.transferTitle}>بيانات المحضر السابق</span>
                        </div>
                      </div>
                      <div className={styles.transferContent}>
                        {caseData.reportNumber && (
                          <div className={styles.transferInfoRow}>
                            <span className={styles.transferLabel}>رقم المحضر:</span>
                            <span className={styles.transferValue}>
                              {caseData.reportNumber}{caseData.caseYear ? `/${caseData.caseYear}` : ''}
                            </span>
                          </div>
                        )}
                        {caseData.reportLocation && (
                          <div className={styles.transferInfoRow}>
                            <span className={styles.transferLabel}>الجهة المختصة:</span>
                            <span className={styles.transferValue}>{caseData.reportLocation}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}



                {/* إضافة قسم بيانات الملفات القديمة - جميع الدرجات السابقة */}
                {group.type === 'identification' && caseData.originalCaseId && (() => {
                  // تحديد عنوان القسم حسب درجة الملف القديم
                  const getOldFileTitle = (originalDegree) => {
                    if (!originalDegree) return 'بيانات الملف القديم';

                    const degreeMap = {
                      // الدرجة الأولى
                      'ابتدائي': 'بيانات ملف أول درجة',
                      'ابتدائية': 'بيانات ملف أول درجة',
                      'جنايات': 'بيانات ملف أول درجة',
                      'جنح': 'بيانات ملف أول درجة',
                      'مخالفات': 'بيانات ملف أول درجة',
                      'أحوال شخصية': 'بيانات ملف أول درجة',
                      'تجاري': 'بيانات ملف أول درجة',
                      'تجارية': 'بيانات ملف أول درجة',
                      'عمالي': 'بيانات ملف أول درجة',
                      'عمالية': 'بيانات ملف أول درجة',
                      'إداري': 'بيانات ملف أول درجة',
                      'إدارية': 'بيانات ملف أول درجة',
                      'مدني': 'بيانات ملف أول درجة',
                      'مدنية': 'بيانات ملف أول درجة',

                      // الدرجة الثانية
                      'استئناف': 'بيانات ملف ثاني درجة',
                      'استئنافي': 'بيانات ملف ثاني درجة',
                      'استئنافية': 'بيانات ملف ثاني درجة',
                      'عليا إدارية': 'بيانات ملف ثاني درجة',
                      'محكمة الاستئناف': 'بيانات ملف ثاني درجة',

                      // الدرجة الثالثة
                      'نقض': 'بيانات ملف ثالث درجة',
                      'محكمة النقض': 'بيانات ملف ثالث درجة',
                      'دستورية عليا': 'بيانات ملف ثالث درجة',
                      'المحكمة الدستورية العليا': 'بيانات ملف ثالث درجة'
                    };

                    return degreeMap[originalDegree] || 'بيانات الملف القديم';
                  };

                  // البحث في جميع القضايا المرتبطة للحصول على بيانات كل الدرجات
                  const getAllPreviousDegreesData = async () => {
                    const allPreviousData = [];

                    try {
                      // الحصول على جميع القضايا بما في ذلك المخفية
                      const { getAllCases } = await import('../../services/StorageService');
                      const allCases = await getAllCases(currentUser.uid);

                      // البحث عن جميع القضايا المرتبطة بنفس السلسلة
                      const relatedCases = [];

                      // البحث عن القضية الأصلية الأولى
                      let currentOriginalId = caseData.originalCaseId;
                      while (currentOriginalId) {
                        const originalCase = allCases.find(c => c.id === currentOriginalId);
                        if (originalCase) {
                          relatedCases.unshift(originalCase); // إضافة في البداية للترتيب الصحيح
                          currentOriginalId = originalCase.originalCaseId;
                        } else {
                          break;
                        }
                      }

                      console.log('القضايا المرتبطة الموجودة:', relatedCases.map(c => ({
                        id: c.id,
                        degree: c.caseDegree,
                        number: c.fullCaseNumber,
                        clientName: c.clientName,
                        opponentName: c.opponentName
                      })));

                      // مقارنة كل قضية مرتبطة مع القضية الحالية
                      relatedCases.forEach(relatedCase => {
                        const changedFields = [];

                        // مقارنة البيانات مع القضية الحالية
                        if (relatedCase.fullCaseNumber && relatedCase.fullCaseNumber !== caseData.fullCaseNumber) {
                          changedFields.push({
                            label: 'رقم القضية',
                            value: relatedCase.fullCaseNumber
                          });
                        }

                        if (relatedCase.courtLocation && relatedCase.courtLocation !== caseData.courtLocation) {
                          changedFields.push({
                            label: 'المحكمة',
                            value: relatedCase.courtLocation
                          });
                        }

                        if (relatedCase.clientName && relatedCase.clientName !== caseData.clientName) {
                          changedFields.push({
                            label: 'اسم الموكل',
                            value: relatedCase.clientName
                          });
                        }

                        if (relatedCase.opponentName && relatedCase.opponentName !== caseData.opponentName) {
                          changedFields.push({
                            label: 'اسم الخصم',
                            value: relatedCase.opponentName
                          });
                        }

                        if (relatedCase.caseDescription && relatedCase.caseDescription !== caseData.caseDescription) {
                          changedFields.push({
                            label: 'وصف القضية',
                            value: relatedCase.caseDescription
                          });
                        }

                        if (relatedCase.caseCategory && relatedCase.caseCategory !== caseData.caseCategory) {
                          changedFields.push({
                            label: 'نوع الدعوى',
                            value: relatedCase.caseCategory
                          });
                        }

                        if (relatedCase.circleNumber && relatedCase.circleNumber !== caseData.circleNumber) {
                          changedFields.push({
                            label: 'رقم الدائرة',
                            value: relatedCase.circleNumber
                          });
                        }

                        if (changedFields.length > 0) {
                          allPreviousData.push({
                            degree: relatedCase.caseDegree,
                            title: getOldFileTitle(relatedCase.caseDegree),
                            changedFields: changedFields,
                            caseId: relatedCase.id
                          });
                        }
                      });

                    } catch (error) {
                      console.error('خطأ في جلب القضايا المرتبطة:', error);

                      // البديل الاحتياطي: استخدام البيانات المحفوظة مباشرة
                      const changedFields = [];

                      if (caseData.originalCaseNumber && caseData.originalCaseNumber !== caseData.fullCaseNumber) {
                        changedFields.push({
                          label: 'رقم القضية',
                          value: caseData.originalCaseNumber
                        });
                      }

                      if (caseData.originalCourtLocation && caseData.originalCourtLocation !== caseData.courtLocation) {
                        changedFields.push({
                          label: 'المحكمة',
                          value: caseData.originalCourtLocation
                        });
                      }

                      if (caseData.originalClientName && caseData.originalClientName !== caseData.clientName) {
                        changedFields.push({
                          label: 'اسم الموكل',
                          value: caseData.originalClientName
                        });
                      }

                      if (caseData.originalOpponentName && caseData.originalOpponentName !== caseData.opponentName) {
                        changedFields.push({
                          label: 'اسم الخصم',
                          value: caseData.originalOpponentName
                        });
                      }

                      if (caseData.originalCaseDescription && caseData.originalCaseDescription !== caseData.caseDescription) {
                        changedFields.push({
                          label: 'وصف القضية',
                          value: caseData.originalCaseDescription
                        });
                      }

                      if (caseData.originalCaseCategory && caseData.originalCaseCategory !== caseData.caseCategory) {
                        changedFields.push({
                          label: 'نوع الدعوى',
                          value: caseData.originalCaseCategory
                        });
                      }

                      if (caseData.originalCircleNumber && caseData.originalCircleNumber !== caseData.circleNumber) {
                        changedFields.push({
                          label: 'رقم الدائرة',
                          value: caseData.originalCircleNumber
                        });
                      }

                      if (changedFields.length > 0) {
                        allPreviousData.push({
                          degree: caseData.originalCaseDegree,
                          title: getOldFileTitle(caseData.originalCaseDegree),
                          changedFields: changedFields
                        });
                      }
                    }

                    return allPreviousData;
                  };

                  // استخدام useState و useEffect للتعامل مع البيانات async
                  const [allPreviousData, setAllPreviousData] = React.useState([]);
                  const [isLoading, setIsLoading] = React.useState(true);
                  const [expandedOldFile, setExpandedOldFile] = React.useState(null); // لتتبع الملف المفتوح

                  React.useEffect(() => {
                    const loadPreviousData = async () => {
                      setIsLoading(true);
                      try {
                        const data = await getAllPreviousDegreesData();
                        setAllPreviousData(data);
                        console.log('جميع بيانات الدرجات السابقة:', data);
                      } catch (error) {
                        console.error('خطأ في تحميل بيانات الدرجات السابقة:', error);
                        setAllPreviousData([]);
                      } finally {
                        setIsLoading(false);
                      }
                    };

                    loadPreviousData();
                  }, [caseData.id, caseData.originalCaseId]);

                  // عرض الأقسام فقط إذا كان هناك بيانات متغيرة
                  if (isLoading) {
                    return (
                      <div className={styles.transferDegreeSection}>
                        <div className={`${styles.transferDegreeBox} ${styles.identificationTheme}`}>
                          <div className={styles.transferContent}>
                            <div className={styles.transferInfoRow}>
                              <span className={styles.transferValue}>جاري تحميل بيانات الملفات السابقة...</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  }

                  if (allPreviousData.length === 0) return null;

                  // دالة للتحكم في فتح وإغلاق الأقسام
                  const toggleOldFileExpansion = (degreeIndex) => {
                    if (expandedOldFile === degreeIndex) {
                      setExpandedOldFile(null); // إغلاق إذا كان مفتوحاً
                    } else {
                      setExpandedOldFile(degreeIndex); // فتح وإغلاق الآخرين
                    }
                  };

                  return (
                    <>
                      {allPreviousData.map((degreeData, degreeIndex) => (
                        <div key={degreeIndex} className={styles.transferDegreeSection}>
                          <div className={`${styles.transferDegreeBox} ${styles.identificationTheme}`}>
                            {/* العنوان القابل للضغط */}
                            <div
                              className={styles.transferHeader}
                              onClick={() => toggleOldFileExpansion(degreeIndex)}
                              style={{ cursor: 'pointer' }}
                            >
                              <div className={styles.transferTitleSection}>
                                <FaFileAlt className={styles.transferIcon} />
                                <span className={styles.transferTitle}>{degreeData.title}</span>
                              </div>
                              {/* أيقونة التوسيع */}
                              <div className={styles.expandButton}>
                                {expandedOldFile === degreeIndex ? <FaChevronUp /> : <FaChevronDown />}
                              </div>
                            </div>

                            {/* المحتوى القابل للتوسيع */}
                            {expandedOldFile === degreeIndex && (
                              <div className={styles.transferContent}>
                                {degreeData.changedFields.map((field, fieldIndex) => (
                                  <div key={fieldIndex} className={styles.transferInfoRow}>
                                    <span className={styles.transferLabel}>{field.label}:</span>
                                    <span className={styles.transferValue}>{field.value}</span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </>
                  );
                })()}
              </div>
            )}

            {/* زر عرض المزيد */}
            {(group.items.filter(item => !item.primary).length > 0 ||
              (group.type === 'identification' && caseData.originalCaseId) ||
              (group.type === 'parties' && canTransferStatus())) && (
              <div className={styles.expandToggle} onClick={() => toggleCardExpand(group.type)}>
                {expandedCards[group.type] ? 'عرض أقل' : 'عرض المزيد'}
                {expandedCards[group.type] ? <FaChevronUp /> : <FaChevronDown />}
              </div>
            )}
          </div>
        ))}

        {/* البطاقة الرابعة: الأرشيف الزمني (المكون الأصلي) */}
        {/* يظهر الأرشيف الزمني لجميع حالات القضايا بما في ذلك "قيد النظر" */}
        <div className={`${styles.coloredCard} ${styles.timelineCard}`}>
          {/* نستخدم المكون الأصلي هنا ونمرر له البيانات اللازمة */}
          <CaseArchiveView
            caseData={caseData}
            cardClassName={`${styles.coloredCard} ${styles.timelineCard}`}
          />
        </div>

      </div>

      {/* نافذة تأكيد تحويل الدرجة */}
      {showTransferConfirmation && (
        <div className={styles.transferConfirmationOverlay}>
          <div className={styles.transferConfirmationDialog}>
            <div className={styles.transferConfirmationIcon}>
              <FaExchangeAlt />
            </div>
            <h3 className={styles.transferConfirmationTitle}>تأكيد تحويل الدرجة القضائية</h3>
            <p className={styles.transferConfirmationMessage}>
              هل أنت متأكد من تحويل هذه القضية إلى درجة قضائية جديدة؟
              <br />
              سيتم إخفاء القضية الحالية من القائمة الرئيسية والانتقال لإضافة دعوى جديدة مع الاحتفاظ بالبيانات الأساسية.
            </p>
            <div className={styles.transferConfirmationActions}>
              <button
                onClick={handleTransferConfirm}
                className={styles.transferConfirmButton}
              >
                <FaExchangeAlt />
                تأكيد التحويل
              </button>
              <button
                onClick={handleTransferCancel}
                className={styles.transferCancelButton}
              >
                <FaTimes />
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تأكيد تحويل حالة الملف */}
      {showStatusTransferConfirmation && (
        <div className={styles.transferConfirmationOverlay}>
          <div className={styles.transferConfirmationDialog}>
            <div className={styles.transferConfirmationIcon}>
              <FaExchangeAlt />
            </div>
            <h3 className={styles.transferConfirmationTitle}>تحويل حالة الملف</h3>
            <p className={styles.transferConfirmationMessage}>
              الحالة الحالية: "{caseData.caseStatus}"
            </p>

            {/* خيارات التحويل حسب الحالة الحالية */}
            <div className={styles.statusOptionsContainer}>
              {caseData.caseStatus === 'قيد النظر' && (
                <>
                  <p className={styles.questionText}>اختر الحالة الجديدة:</p>
                  <div className={styles.statusOptions}>
                    <button
                      className={`${styles.statusOptionButton} ${selectedNewStatus === 'محضر' ? styles.selected : ''}`}
                      onClick={() => handleStatusSelection('محضر')}
                    >
                      محضر
                    </button>
                    <button
                      className={`${styles.statusOptionButton} ${selectedNewStatus === 'دعوى قضائية' ? styles.selected : ''}`}
                      onClick={() => handleStatusSelection('دعوى قضائية')}
                    >
                      دعوى قضائية
                    </button>
                  </div>
                </>
              )}

              {caseData.caseStatus === 'محضر' && (
                <>
                  <p className={styles.questionText}>هل تريد رفع دعوى قضائية؟</p>
                  <div className={styles.statusOptions}>
                    <button
                      className={`${styles.statusOptionButton} ${selectedNewStatus === 'دعوى قضائية' ? styles.selected : ''}`}
                      onClick={() => handleStatusSelection('دعوى قضائية')}
                    >
                      نعم، رفع دعوى قضائية
                    </button>
                  </div>
                </>
              )}

              {/* تم إزالة خيارات التحويل للدعوى القضائية لأنه لا يمكن التحويل منها */}

              {/* حقل تاريخ رفع الدعوى */}
              {selectedNewStatus === 'دعوى قضائية' && (
                <div className={styles.dateInputContainer}>
                  <label className={styles.dateLabel}>
                    تاريخ رفع الدعوى <span style={{color: 'red'}}>*</span>
                  </label>
                  <input
                    type="date"
                    value={lawsuitDate}
                    onChange={(e) => setLawsuitDate(e.target.value)}
                    className={styles.dateInput}
                  />
                </div>
              )}

              {/* حقل تاريخ كتابة المحضر */}
              {selectedNewStatus === 'محضر' && (
                <div className={styles.dateInputContainer}>
                  <label className={styles.dateLabel}>
                    تاريخ كتابة المحضر <span style={{color: 'red'}}>*</span>
                  </label>
                  <input
                    type="date"
                    value={reportDate}
                    onChange={(e) => setReportDate(e.target.value)}
                    className={styles.dateInput}
                  />
                </div>
              )}
            </div>

            <div className={styles.transferConfirmationActions}>
              <button
                onClick={handleStatusTransferConfirm}
                className={styles.transferConfirmButton}
                disabled={!selectedNewStatus ||
                         (selectedNewStatus === 'دعوى قضائية' && !lawsuitDate) ||
                         (selectedNewStatus === 'محضر' && !reportDate)}
              >
                <FaExchangeAlt />
                تأكيد التحويل
              </button>
              <button
                onClick={handleStatusTransferCancel}
                className={styles.transferCancelButton}
              >
                <FaTimes />
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CaseInfoGroups;

