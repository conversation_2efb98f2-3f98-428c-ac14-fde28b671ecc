import React, { useState, useEffect } from 'react';
import { FaCog, FaListAlt } from 'react-icons/fa';
import { useLocation } from 'react-router-dom';
import TopBar from './topbar/TopBar';
import DeferralTemplates from './DeferralTemplates';
import styles from './Settings.module.css';

const Settings = ({ currentUser }) => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('general');

  // التحقق من state المرسل من التنقل لتحديد التبويب النشط
  useEffect(() => {
    if (location.state && location.state.activeTab) {
      setActiveTab(location.state.activeTab);
    }
  }, [location.state]);

  if (!currentUser) {
    return <div>جاري التحقق من حالة المستخدم...</div>;
  }

  return (
    <div className={styles.settingsContainer}>
      <TopBar currentUser={currentUser} />
      <div className={styles.content}>
        <h1 className={styles.title}>
          <FaCog className={styles.icon} />
          الإعدادات
        </h1>

        {/* تبويبات الأقسام */}
        <div className={styles.tabs}>
          <button
            className={`${styles.tabButton} ${activeTab === 'general' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('general')}
          >
            إعدادات عامة
          </button>
          <button
            className={`${styles.tabButton} ${activeTab === 'deferralTemplates' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('deferralTemplates')}
          >
            <FaListAlt className={styles.tabIcon} />
            قوالب التأجيلات
          </button>
        </div>

        {/* محتوى القسم */}
        <div className={styles.tabContent}>
          {activeTab === 'general' && (
            <div className={styles.section}>
              <h2>إعدادات عامة</h2>
              <p>لم يتم إضافة إعدادات عامة بعد. يمكنك إضافة إعدادات مثل تغيير كلمة المرور أو إعدادات الحساب هنا.</p>
            </div>
          )}
          {activeTab === 'deferralTemplates' && (
            <div className={styles.section}>
              <DeferralTemplates currentUser={currentUser} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;