:root {
  /* لوحة الألوان الحمراء الإضافية */
  --red-primary: #622872; /* بنفسجي داكن */
  --red-secondary: #caa5cb; /* وردي فاتح */
  --red-light: #e8ddea; /* وردي باهت */
  --red-lightest: #faeaf6; /* وردي فاتح جداً */

  /* لوحة الألوان الزرقاء الإضافية */
  --blue-darkest: #00033a; /* أزرق داكن جداً */
  --blue-dark-alt: #162647; /* أزرق داكن بديل */
  --blue-medium-alt: #163473; /* أزرق متوسط بديل */
  --accent-gold: #d2ab17; /* ذهبي */
}

.pageContainer {
  background-color: #f5f7fa;
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Cairo', sans-serif;
  min-height: 100vh;
}

.contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Main Panel Styles */
.mainPanel {
  background: white;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e1e8ed;
}

.headerTitle {
  display: flex;
  align-items: center;
  gap: 10px;
}

.headerIcon {
  font-size: 1.2rem;
  color: #4267B2;
}

.pageTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.accountStatus {
  font-size: 0.85rem;
}

/* أزرار الإجراءات في الإشعارات */
.notificationActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e1e8ed;
}

.completeButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.completeButton:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.completeButton .buttonIcon {
  font-size: 12px;
}

.onlineStatus, .localStatus {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 20px;
  font-weight: 500;
}

.onlineStatus {
  background-color: #ebf5fb;
  color: #3498db;
}

.localStatus {
  background-color: #eafaf1;
  color: #2ecc71;
}

.statusIcon {
  font-size: 0.8rem;
}

/* Filter Tabs Styles */
.filterTabs {
  display: flex;
  padding: 0 10px;
  border-bottom: 1px solid #e1e8ed;
  background-color: #f8f9fa;
}

.filterTab {
  padding: 12px 15px;
  border: none;
  background: transparent;
  font-family: 'Tajawal', sans-serif;
  font-size: 0.9rem;
  color: #5a6b8c;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.filterTab:hover {
  color: #4267B2;
}

.activeTab {
  color: #4267B2;
  font-weight: 600;
}

.activeTab::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #4267B2;
}

.tabCount {
  background-color: #ebeef2;
  color: #5a6b8c;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 600;
}

.activeTab .tabCount {
  background-color: #4267B2;
  color: white;
}



/* Notifications Container */
.notificationsContainer {
  padding: 20px;
}

/* Notifications List */
.notificationsList {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Notification Card */
.notificationCard {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: all 0.2s ease;
}

.notificationCard:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notificationCard.high {
  border-right: 3px solid #e74c3c;
}

.notificationCard.medium {
  border-right: 3px solid #f39c12;
}

/* Card Header */
.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e1e8ed;
}

.notificationMeta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typeIcon {
  font-size: 1rem;
  color: #4267B2;
}

.notificationType {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

.accountBadge {
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.onlineBadge {
  background-color: #ebf5fb;
  color: #3498db;
}

.localBadge {
  background-color: #eafaf1;
  color: #2ecc71;
}

.date {
  font-size: 0.8rem;
  color: #7f8c8d;
  background: #f8f9fa;
  padding: 3px 8px;
  border-radius: 4px;
}

/* Card Body */
.cardBody {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.caseInfo {
  margin-bottom: 10px;
}

.caseNumber {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.caseIcon {
  color: #4267B2;
  font-size: 0.9rem;
}

.clientInfo, .courtInfo {
  display: flex;
  margin-bottom: 5px;
}

.infoLabel {
  font-weight: 600;
  color: #34495e;
  font-size: 0.85rem;
  margin-left: 5px;
}

.infoValue {
  color: #2c3e50;
  font-size: 0.85rem;
}

.notificationDetails {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
  margin-top: 5px;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detailLabel {
  font-weight: 600;
  color: #34495e;
  font-size: 0.85rem;
}

.detailValue {
  color: #2c3e50;
  font-size: 0.85rem;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.emptyFace {
  width: 80px;
  height: 80px;
  background: #f1f3f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.emptyIcon {
  font-size: 2.5rem;
  color: #bdc3c7;
}

.emptyState h3 {
  font-size: 1.2rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.emptyState p {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin: 0;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(66, 103, 178, 0.2);
  border-radius: 50%;
  border-top-color: #4267B2;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loadingContainer p {
  font-size: 0.9rem;
  color: #2c3e50;
}

/* Error State */
.errorMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
}

.errorIcon {
  font-size: 2rem;
  color: #e74c3c;
  margin-bottom: 15px;
}

.errorMessage p {
  font-size: 0.9rem;
  color: #2c3e50;
  margin: 0;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .contentWrapper {
    padding: 15px;
  }

  .filterTabs {
    overflow-x: auto;
    padding: 0 5px;
  }

  .filterTab {
    padding: 10px;
    white-space: nowrap;
  }

  .notificationsContainer {
    padding: 15px;
  }

  .notificationCard {
    padding: 12px;
  }

  .cardHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .date {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .contentWrapper {
    padding: 10px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .filterTab {
    padding: 8px;
    font-size: 0.8rem;
  }

  .tabCount {
    font-size: 0.7rem;
    padding: 1px 6px;
  }

  .notificationsContainer {
    padding: 10px;
  }

  .notificationCard {
    padding: 10px;
  }

  .notificationType {
    font-size: 0.8rem;
  }

  .accountBadge {
    font-size: 0.7rem;
    padding: 1px 6px;
  }

  .caseNumber {
    font-size: 1rem;
  }

  .infoLabel, .infoValue, .detailLabel, .detailValue {
    font-size: 0.8rem;
  }
}





