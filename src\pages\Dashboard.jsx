import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaGlobe, FaMobileAlt } from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import styles from './Dashboard.module.css';
import { db } from '../config/firebaseConfig';
import { doc, getDoc } from "firebase/firestore";
import { getActiveAccount, setActiveAccount } from '../services/StorageService';

const Dashboard = ({ currentUser }) => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({ delayedCasesCount: 0, totalCasesCount: 0 });
  const [loading, setLoading] = useState(true);
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());

  const CACHE_KEY = `dashboard_stats_${currentUser?.uid || 'guest'}`;
  const CACHE_TTL = 5 * 60 * 1000; // 5 دقائق

  // لم نعد بحاجة إلى وظيفة تبديل الحساب النشط هنا
  // تم نقلها إلى صفحة البروفايل

  useEffect(() => {
    const fetchStats = async (bypassCache = false) => {
      const cachedData = localStorage.getItem(CACHE_KEY);
      if (cachedData && !bypassCache) {
        const { data, timestamp, account } = JSON.parse(cachedData);
        // استخدام التخزين المؤقت فقط إذا كان الحساب النشط هو نفسه المخزن
        if (Date.now() - timestamp < CACHE_TTL && account === activeAccount) {
          setStats(data);
          setLoading(false);
          return;
        }
      }

      setLoading(true);
      try {
        // إذا كان الحساب أونلاين
        if (activeAccount === 'online') {
          // إذا كان الحساب أونلاين وغير متصل بالإنترنت
          if (!navigator.onLine) {
            setStats({ totalCasesCount: 0 });
            setLoading(false);
            return;
          }

          const statsRef = doc(db, 'stats', 'global');
          const statsDoc = await getDoc(statsRef);
          if (statsDoc.exists()) {
            const data = statsDoc.data();
            setStats(data);
            localStorage.setItem(CACHE_KEY, JSON.stringify({
              data,
              timestamp: Date.now(),
              account: activeAccount
            }));
          }
        } else {
          // إذا كان الحساب محلي، استخدم بيانات محلية
          const statsData = {
            totalCasesCount: 0 // يمكن تحسين هذا لاحقًا لحساب الإحصائيات المحلية
          };

          setStats(statsData);

          localStorage.setItem(CACHE_KEY, JSON.stringify({
            data: statsData,
            timestamp: Date.now(),
            account: activeAccount
          }));
        }
      } catch (error) {
        console.error("Error fetching stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
    const interval = setInterval(() => {
      fetchStats(true);
    }, 30000);

    return () => clearInterval(interval);
  }, [activeAccount]);

  return (
    <div className={styles.dashboard}>
      <TopBar currentUser={currentUser} />
      <main className={styles.content}>
        <div className={styles.header}>
          <h1>مرحباً بك في نظام إدارة القضايا</h1>
          <div className={styles.accountStatus}>
            <span>الحساب النشط حالياً: </span>
            <strong className={activeAccount === 'online' ? styles.onlineText : styles.localText}>
              {activeAccount === 'online' ? (
                <>
                  <FaGlobe className={styles.accountIcon} /> أونلاين
                </>
              ) : (
                <>
                  <FaMobileAlt className={styles.accountIcon} /> محلي
                </>
              )}
            </strong>
            <span className={styles.accountNote}>
              (يمكنك تغيير الحساب من صفحة الملف الشخصي)
            </span>
          </div>
        </div>
        {loading ? (
          <div style={{ textAlign: "center", padding: '20px' }}>
            <div style={{ border: "4px solid rgba(0, 0, 0, 0.1)", borderLeft: "4px solid #000", borderRadius: "50%", width: "30px", height: "30px", animation: "spin 1s linear infinite", margin: "0 auto" }}></div>
            <p>جاري تحميل الإحصائيات...</p>
            <style>
              {`
                @keyframes spin {
                  0% { transform: rotate(0deg); }
                  100% { transform: rotate(360deg); }
                }
              `}
            </style>
          </div>
        ) : (
          <>
            <div className={styles.stats}>
              <div className={styles.statCard}>
                <span className={styles.statValue}>{stats.totalCasesCount || 0}</span>
                <span className={styles.statLabel}>إجمالي القضايا</span>
              </div>
            </div>
            <p>اختر من الخيارات أدناه للبدء</p>
            <div className={styles.grid}>
              <div className={styles.card} onClick={() => navigate('/reports')}>
                <h3>التقارير</h3>
                <p>عرض التقارير</p>
              </div>
            </div>
          </>
        )}
      </main>
    </div>
  );
};

export default Dashboard;