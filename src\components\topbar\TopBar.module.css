/* استخدام متغيرات CSS من الملف الرئيسي */

/* شريط التنقل العلوي */
.topBar {
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  background: linear-gradient(135deg, #4c5f7a, #6b7fa3, #8fa4c7, #b3c9eb);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(76, 95, 122, 0.3);
  color: white;
  animation: slideIn 0.5s ease;
}

/* تأثير الانزلاق */
@keyframes slideIn {
  from {
    margin-top: -20px;
    opacity: 0;
  }
  to {
    margin-top: 0;
    opacity: 1;
  }
}

/* القسم الأيسر */
.leftSection {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

/* القسم الأيمن */
.rightSection {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* تنسيق الأزرار الدائرية */
.iconWrapper {
  position: relative;
}

.iconButton {
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all var(--transition-normal);
  padding: 8px;
  margin: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.iconButton:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  color: white;
}

/* زر الإضافة بتدرج بنفسجي إلى أزرق سماوي */
.iconButton[aria-label="إضافة قضية جديدة"] {
  background: linear-gradient(135deg, #d946ef, #8b5cf6, #3b82f6, #06b6d4);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
  position: relative;
  overflow: hidden;
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.iconButton[aria-label="إضافة قضية جديدة"]:hover {
  background: linear-gradient(135deg, #c026d3, #7c3aed, #2563eb, #0891b2);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.6);
  color: white;
  animation: gradientShiftHover 2s ease infinite;
}

/* تأثير تحريك التدرج */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradientShiftHover {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.iconButton[aria-label="إضافة قضية جديدة"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.iconButton[aria-label="إضافة قضية جديدة"]:hover::before {
  left: 100%;
}

/* زر الإشعارات بلون أصفر محمر (أصفر أكثر) */
.iconButton[aria-label="الإشعارات"] {
  background: linear-gradient(135deg, #ffd700, #ffaa00);
  border: 1px solid #ffaa00;
  color: white;
  box-shadow: 0 2px 8px rgba(255, 170, 0, 0.4);
}

.iconButton[aria-label="الإشعارات"]:hover {
  background: linear-gradient(135deg, #ffcc00, #ff8800);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 170, 0, 0.5);
  color: white;
}



/* زر المجموعات */
.groupsButton {
  background-color: #000;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all var(--transition-normal);
  padding: 8px;
  margin: 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.groupsButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* أنماط مؤشر الحساب */
.accountIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--neutral-100);
  border: 1px solid var(--neutral-200);
  position: relative;
  box-shadow: var(--shadow-sm);
}

.onlineIcon {
  color: var(--info);
}

.localIcon {
  color: var(--success);
}

.tooltip {
  display: none;
}

.notificationBadge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--error);
  color: white;
  border-radius: var(--radius-full);
  width: 20px;
  height: 20px;
  font-size: 0.7rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: var(--shadow-sm);
  animation: pulse 2s infinite;
}

/* تأثير النبض */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* قائمة الإشعارات */
.notificationDropdown {
  position: absolute;
  top: 45px;
  right: 0;
  min-width: 280px;
  max-width: 90vw;
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 0.75rem;
  z-index: 1050;
  max-height: 50vh;
  overflow-y: auto;
  color: var(--neutral-800);
}

.notificationItem {
  padding: 0.75rem;
  border-bottom: 1px solid var(--neutral-200);
  transition: all var(--transition-fast);
  border-radius: var(--radius-sm);
}

.notificationItem:hover {
  background-color: var(--neutral-100);
}

.notificationItem:last-child {
  border-bottom: none;
}

.notificationItem span {
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--neutral-900);
  display: block;
  margin-bottom: 4px;
}

.notificationItem p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--neutral-600);
  line-height: 1.4;
}

/* ملف المستخدم */
.profileWrapper {
  position: relative;
}

.profileButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  cursor: pointer;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  margin: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.profileButton:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.profileButton::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0)
  );
  pointer-events: none;
}

/* أنماط مشتركة للأفاتار */
.userAvatarOnline,
.userAvatarLocal {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem;
  flex-shrink: 0;
  position: relative;
  background-color: white;
  border: 1px solid #e1e8ed;
}

/* أنماط الأفاتار للحساب الأونلاين */
.userAvatarOnline {
  border-color: rgba(52, 152, 219, 0.3);
}

.avatarIconOnline {
  width: 18px;
  height: 18px;
  color: #3498db;
  filter: drop-shadow(0 0 5px rgba(52, 152, 219, 0.8));
  position: relative;
  animation: pulseOnline 2s infinite alternate;
}

/* أنماط الأفاتار للحساب المحلي */
.userAvatarLocal {
  border-color: rgba(46, 204, 113, 0.3);
}

.avatarIconLocal {
  width: 18px;
  height: 18px;
  color: #2ecc71;
  filter: drop-shadow(0 0 5px rgba(46, 204, 113, 0.8));
  position: relative;
  animation: pulseLocal 2s infinite alternate;
}

/* تأثيرات الرسوم المتحركة */
@keyframes pulseOnline {
  0% {
    filter: drop-shadow(0 0 3px rgba(52, 152, 219, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 8px rgba(52, 152, 219, 1));
  }
}

@keyframes pulseLocal {
  0% {
    filter: drop-shadow(0 0 3px rgba(46, 204, 113, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 8px rgba(46, 204, 113, 1));
  }
}

.userInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  overflow: hidden;
}

.userName {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  direction: rtl;
  display: flex;
  align-items: center;
  gap: 6px;
}



/* القائمة المنسدلة */
.profileDropdown {
  position: absolute;
  top: 50px;
  right: 0;
  min-width: 180px;
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 0.5rem;
  z-index: 1050;
  overflow-y: auto;
  color: var(--neutral-800);
}

.dropdownItem {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  color: var(--neutral-700);
  text-align: right;
  cursor: pointer;
  font-size: 0.9rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-weight: 500;
}

.dropdownItem:hover {
  background-color: var(--neutral-100);
  color: var(--primary-color);
}

.dropdownItem svg {
  color: var(--neutral-500);
  width: 18px;
  height: 18px;
}

/* Media Query للشاشات الصغيرة (تحسين الهاتف) */
@media (max-width: 768px) {
  .topBar {
    padding: 0 1rem;
    height: 64px;
  }

  .rightSection {
    gap: 0.75rem;
  }

  .iconButton, .groupsButton {
    width: 38px;
    height: 38px;
    padding: 8px;
  }

  .notificationBadge {
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    border-width: 1px;
  }

  .userAvatarOnline,
  .userAvatarLocal {
    width: 32px;
    height: 32px;
  }

  .avatarIconOnline,
  .avatarIconLocal {
    width: 14px;
    height: 14px;
  }
}

  .profileButton {
    padding: 0.4rem 0.6rem;
    gap: 0.5rem;
    min-width: 120px;
  }

  .userName {
    font-size: 0.85rem;
    max-width: 80px;
  }

  .statusIndicatorOnline,
  .statusIndicatorLocal {
    width: 6px;
    height: 6px;
  }

  .profileDropdown {
    min-width: 160px;
  }

  .notificationDropdown {
    min-width: 250px;
  }

  .dropdownItem {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
    gap: 0.5rem;
  }

@media (max-width: 480px) {
  .topBar {
    padding: 0 0.75rem;
    height: 60px;
  }

  .rightSection {
    gap: 0.5rem;
  }

  .iconButton, .groupsButton {
    width: 36px;
    height: 36px;
    padding: 6px;
  }

  .notificationBadge {
    width: 16px;
    height: 16px;
    font-size: 0.65rem;
  }

  .userAvatarOnline,
  .userAvatarLocal {
    width: 30px;
    height: 30px;
  }

  .avatarIconOnline,
  .avatarIconLocal {
    width: 12px;
    height: 12px;
  }

  .profileButton {
    padding: 0.3rem 0.5rem;
    gap: 0.3rem;
    min-width: 100px;
  }

  .userName {
    font-size: 0.8rem;
    max-width: 60px;
  }

  .statusIndicatorOnline,
  .statusIndicatorLocal {
    width: 5px;
    height: 5px;
    margin-right: 2px;
  }

  .profileDropdown {
    min-width: 140px;
  }

  .notificationDropdown {
    min-width: 220px;
  }

  .dropdownItem {
    padding: 0.5rem 0.7rem;
    font-size: 0.8rem;
    gap: 0.4rem;
  }
}