import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './SignUp.css';
import { auth, db } from '../config/firebaseConfig';
import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth";
import { collection, doc, setDoc } from "firebase/firestore";
import { checkSignupLockout, recordSignupFailure, clearSignupLockout } from '../utils/LockoutManager';

function SignUp() {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [signupAttempts, setSignupAttempts] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    const lockoutStatus = checkSignupLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(true);
    } else {
      setSignupAttempts(lockoutStatus.attempts);
    }
  }, []);

  const handleSubmit = async (event) => {
    event.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);

    const lockoutData = localStorage.getItem('signupLockout');
    let attempts = signupAttempts;
    if (lockoutData) {
      const { timestamp, attempts: storedAttempts } = JSON.parse(lockoutData);
      const now = Date.now();
      if (now - timestamp < LOCKOUT_DURATION && storedAttempts >= MAX_ATTEMPTS) {
        const remainingMinutes = Math.ceil((LOCKOUT_DURATION - (now - timestamp)) / 1000 / 60);
        setErrorMessage(`تم قفل إنشاء الحساب مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${remainingMinutes} دقيقة${remainingMinutes > 1 ? 'ات' : ''}.`);
        setLoading(true);
        return;
      } else if (now - timestamp >= LOCKOUT_DURATION) {
        localStorage.removeItem('signupLockout');
        attempts = 0;
      } else {
        attempts = storedAttempts;
      }
    }

    if (!email.trim()) {
      setErrorMessage('يرجى إدخال البريد الإلكتروني.');
      setLoading(false);
      return;
    }

    if (!password.trim()) {
      setErrorMessage('يرجى إدخال كلمة المرور.');
      setLoading(false);
      return;
    }

    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email.trim(), password);
      const user = userCredential.user;

      console.log('تم إنشاء حساب بنجاح:', user);

      if (username.trim()) {
        await updateProfile(user, { displayName: username.trim() });
        console.log('تم تحديث اسم المستخدم:', username.trim());
      }

      await setDoc(doc(db, 'users', user.uid), {
        username: username.trim() || null,
        email: user.email,
        phone: phone.trim() || null,
        role: 'محامي',
        createdAt: new Date().toISOString(),
      });

      console.log('تم حفظ بيانات المستخدم في Firestore');

      setSuccessMessage('تم إنشاء الحساب بنجاح! سيتم توجيهك إلى صفحة تسجيل الدخول.');
      localStorage.removeItem('signupLockout');
      setSignupAttempts(0);

      setTimeout(() => {
        navigate('/login');
      }, 1500);

    } catch (error) {
      console.error('خطأ في إنشاء الحساب:', error.code, error.message);
      attempts += 1;
      setSignupAttempts(attempts);
      localStorage.setItem('signupLockout', JSON.stringify({
        timestamp: Date.now(),
        attempts,
      }));

      if (attempts >= MAX_ATTEMPTS) {
        const remainingMinutes = Math.ceil(LOCKOUT_DURATION / 1000 / 60);
        setErrorMessage(`تم قفل إنشاء الحساب مؤقتًا بسبب محاولات متكررة. يرجى المحاولة مرة أخرى بعد ${remainingMinutes} دقيقة${remainingMinutes > 1 ? 'ات' : ''}.`);
        setLoading(true);
        return;
      }

      let userFacingMessage = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
      switch (error.code) {
        case 'auth/email-already-in-use':
          userFacingMessage = 'البريد الإلكتروني هذا مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر أو تسجيل الدخول.';
          break;
        case 'auth/invalid-email':
          userFacingMessage = 'صيغة البريد الإلكتروني غير صحيحة. يرجى إدخال بريد إلكتروني صالح.';
          break;
        case 'auth/weak-password':
          userFacingMessage = 'كلمة المرور ضعيفة جدًا. يجب أن تحتوي على 6 أحرف على الأقل.';
          break;
        case 'auth/too-many-requests':
          userFacingMessage = 'تم حظر إنشاء الحساب مؤقتًا بسبب كثرة المحاولات. يرجى المحاولة لاحقًا.';
          break;
        default:
          userFacingMessage = 'حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا.';
      }
      setErrorMessage(userFacingMessage);
    } finally {
      if (attempts < MAX_ATTEMPTS) {
        setLoading(false);
      }
    }
  };

  const handleBack = () => {
    navigate('/login');
  };

  return (
    <div className="signup-container">
      <div className="signup-box">
        <h2>إنشاء حساب جديد</h2>

        {loading && (
          <div className="loading-container">
            <div className="loader"></div>
            <p className="loading-text">جاري إنشاء الحساب...</p>
          </div>
        )}

        {errorMessage && <div className="error-message">{errorMessage}</div>}
        {successMessage && <div className="success-message">{successMessage}</div>}

        <form onSubmit={handleSubmit}>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="username">اسم المستخدم (اختياري)</label>
              <input
                type="text"
                id="username"
                name="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={loading}
                placeholder="أدخل اسم المستخدم"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">البريد الإلكتروني</label>
              <input
                type="email"
                id="email"
                name="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={loading}
                placeholder="أدخل بريدك الإلكتروني"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="password">كلمة المرور</label>
              <input
                type="password"
                id="password"
                name="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={loading}
                placeholder="أدخل كلمة المرور"
              />
            </div>

            <div className="form-group">
              <label htmlFor="phone">رقم الهاتف (اختياري)</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                disabled={loading}
                placeholder="أدخل رقم الهاتف"
              />
            </div>
          </div>

          <div className="button-row">
            <button type="submit" disabled={loading}>
              {loading ? 'جاري إنشاء الحساب...' : 'إنشاء حساب'}
            </button>
            <button
              type="button"
              className="back-button"
              onClick={handleBack}
              disabled={loading}
            >
              رجوع
            </button>
          </div>
        </form>

        <p className="login-link">
          لديك حساب بالفعل؟ <a href="/login">تسجيل الدخول</a>
        </p>
      </div>
    </div>
  );
}

export default SignUp;