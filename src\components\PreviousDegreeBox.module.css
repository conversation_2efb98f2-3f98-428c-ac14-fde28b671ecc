/* Previous Degree Box Container */
.previousDegreeBox {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #059669;
  border-radius: 8px;
  font-size: 0.85rem;
  box-shadow:
    0 2px 6px rgba(5, 150, 105, 0.15),
    0 0 20px rgba(5, 150, 105, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  max-width: 300px;
  margin-left: 16px;
  margin-top: 16px;
  position: relative;
  overflow: hidden;
}

/* تأثير اللمعان المتحرك */
.previousDegreeBox::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    transform: rotate(45deg) translate(-100%, -100%);
  }
  50% {
    transform: rotate(45deg) translate(0%, 0%);
  }
  100% {
    transform: rotate(45deg) translate(100%, 100%);
  }
}

.previousDegreeBox:hover {
  box-shadow:
    0 4px 12px rgba(5, 150, 105, 0.25),
    0 0 30px rgba(5, 150, 105, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
  border-color: #047857;
}

/* Header Section */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.05), rgba(5, 150, 105, 0.02));
  position: relative;
  overflow: hidden;
  border-radius: 6px;
}

/* تأثير لمعان العنوان */
.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s ease;
}

.header:hover {
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(5, 150, 105, 0.05));
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.header:hover::before {
  left: 100%;
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 6px;
}

.icon {
  color: #059669;
  font-size: 0.9rem;
  filter: drop-shadow(0 0 2px rgba(5, 150, 105, 0.3));
  transition: all 0.3s ease;
}

.title {
  font-weight: 600;
  color: #065f46;
  font-size: 0.85rem;
  text-shadow: 0 0 4px rgba(6, 95, 70, 0.2);
  transition: all 0.3s ease;
}

.expandIcon {
  color: #6b7280;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 1px rgba(107, 114, 128, 0.3));
}

.header:hover .icon {
  color: #047857;
  filter: drop-shadow(0 0 4px rgba(4, 120, 87, 0.5));
  transform: scale(1.1);
}

.header:hover .title {
  color: #064e3b;
  text-shadow: 0 0 6px rgba(6, 78, 59, 0.3);
}

.header:hover .expandIcon {
  color: #059669;
  filter: drop-shadow(0 0 2px rgba(5, 150, 105, 0.4));
  transform: scale(1.1);
}

/* Content Section */
.content {
  padding: 12px;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  animation: slideDown 0.2s ease-out;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Info Rows */
.infoRow {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 0.8rem;
  line-height: 1.4;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.infoRow:hover {
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.05), rgba(5, 150, 105, 0.02));
  box-shadow: 0 1px 3px rgba(5, 150, 105, 0.1);
  transform: translateX(2px);
}

.infoRow:last-child {
  margin-bottom: 0;
}

.rowIcon {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 2px;
  flex-shrink: 0;
  filter: drop-shadow(0 0 1px rgba(107, 114, 128, 0.3));
  transition: all 0.3s ease;
}

.infoRow:hover .rowIcon {
  color: #059669;
  filter: drop-shadow(0 0 2px rgba(5, 150, 105, 0.4));
  transform: scale(1.1);
}

.label {
  font-weight: 500;
  color: #374151;
  flex-shrink: 0;
  min-width: 80px;
  text-shadow: 0 0 2px rgba(55, 65, 81, 0.1);
  transition: all 0.3s ease;
}

.infoRow:hover .label {
  color: #1f2937;
  text-shadow: 0 0 3px rgba(31, 41, 55, 0.2);
}

.value {
  color: #1f2937;
  flex: 1;
  word-break: break-word;
  text-shadow: 0 0 1px rgba(31, 41, 55, 0.1);
  transition: all 0.3s ease;
}

.infoRow:hover .value {
  color: #111827;
  text-shadow: 0 0 2px rgba(17, 24, 39, 0.2);
}

/* Action Row */
.actionRow {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.viewButton {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.3);
}

.viewButton:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.4);
}

.viewButton:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .previousDegreeBox {
    max-width: 100%;
    margin-left: 0;
    margin-top: 16px;
    font-size: 0.8rem;
  }

  .header {
    padding: 6px 10px;
  }

  .title {
    font-size: 0.8rem;
  }

  .content {
    padding: 10px;
  }

  .infoRow {
    flex-direction: column;
    gap: 2px;
    margin-bottom: 10px;
  }

  .label {
    min-width: auto;
    font-size: 0.75rem;
  }

  .value {
    font-size: 0.75rem;
    margin-right: 8px;
  }

  .viewButton {
    font-size: 0.7rem;
    padding: 5px 10px;
  }
}

/* Compact Mode */
.previousDegreeBox.compact {
  max-width: 250px;
  font-size: 0.8rem;
}

.previousDegreeBox.compact .header {
  padding: 6px 10px;
}

.previousDegreeBox.compact .title {
  font-size: 0.8rem;
}

.previousDegreeBox.compact .content {
  padding: 8px 10px;
}

.previousDegreeBox.compact .infoRow {
  font-size: 0.75rem;
  margin-bottom: 6px;
}

.previousDegreeBox.compact .label {
  min-width: 70px;
  font-size: 0.7rem;
}

.previousDegreeBox.compact .value {
  font-size: 0.7rem;
}

.previousDegreeBox.compact .viewButton {
  font-size: 0.65rem;
  padding: 4px 8px;
}
