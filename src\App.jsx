import React, { useState, useEffect, createContext, useContext, Suspense, lazy } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { auth } from './config/firebaseConfig';
import { onAuthStateChanged } from "firebase/auth";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import "./styles/App.css";

// Lazy load components
const CaseRegistration = lazy(() => import('./pages/CaseRegistration'));
const CaseDetails = lazy(() => import('./pages/CaseDetails'));
const Login = lazy(() => import('./pages/Login'));
const SignUp = lazy(() => import('./pages/SignUp'));
const Dashboard = lazy(() => import('./pages/Dashboard'));
const ReportsOverview = lazy(() => import('./pages/ReportsOverview'));
const TimelinePage = lazy(() => import('./pages/TimelinePage.jsx'));
const NotificationsPage = lazy(() => import('./pages/NotificationsPage'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const ProtectedRoute = lazy(() => import('./components/ProtectedRoute'));
const AddDeferral = lazy(() => import('./pages/CentralDetailsPage/AddDeferral.jsx'));
const AddAction = lazy(() => import('./pages/CentralDetailsPage/AddAction.jsx'));
const CaseFollowUp = lazy(() => import('./pages/CaseFollowUp'));
const Settings = lazy(() => import('./components/Settings'));
const GroupsManagement = lazy(() => import('./components/GroupsManagement'));

// Create QueryClient
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 دقائق
      cacheTime: 10 * 60 * 1000, // 10 دقائق
      retry: 1, // إعادة المحاولة مرة واحدة فقط
    },
  },
});

// Context for user state
const UserContext = createContext();

const NotFound = () => (
  <div style={{ textAlign: "center", padding: "50px" }}>
    <h2>404 - الصفحة غير موجودة</h2>
    <p>الصفحة التي تبحث عنها غير موجودة. تأكد من المسار أو ارجع إلى الصفحة الرئيسية.</p>
    <button onClick={() => window.location.href = "/dashboard"}>العودة للوحة التحكم</button>
  </div>
);

const Search = () => <div>صفحة البحث (تحت الإنشاء)</div>;

const App = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setLoading(false);
      console.log("App - Auth state changed:", currentUser);
    });

    return () => unsubscribe();
  }, []);

  if (loading) {
    console.log("App - Initializing authentication, showing loading...");
    return <div>جاري التحقق من حالة المصادقة...</div>;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <UserContext.Provider value={{ user, loading }}>
        <Router>
          <Suspense fallback={<div>جاري تحميل الصفحة...</div>}>
            <Routes>
              <Route path="/" element={user ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />} />
              <Route path="/login" element={user ? <Navigate to="/dashboard" replace /> : <Login />} />
              <Route path="/signup" element={user ? <Navigate to="/dashboard" replace /> : <SignUp />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <Dashboard currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/cases"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <CaseRegistration currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/case-details/:caseNumber"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <CaseDetails currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/reports"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <ReportsOverview currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/notifications"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <NotificationsPage currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/radar"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <TimelinePage currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <ProfilePage currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/search"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <Search currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/add-deferral/:caseNumber"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <AddDeferral currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/add-action/:caseNumber"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <AddAction currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/case-follow-up/:caseId"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <CaseFollowUp currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <Settings currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/groups"
                element={
                  <ProtectedRoute user={user} loading={loading}>
                    <GroupsManagement currentUser={user} />
                  </ProtectedRoute>
                }
              />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </Router>
      </UserContext.Provider>
    </QueryClientProvider>
  );
};

export const useUser = () => useContext(UserContext);

export default App;