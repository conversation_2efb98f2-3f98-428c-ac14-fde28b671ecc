// خدمة إدارة التخزين المحلي والوصول إلى البيانات
import { db } from '../config/firebaseConfig';
import { collection, query, where, getDocs, doc, getDoc, addDoc, updateDoc, writeBatch } from 'firebase/firestore';

// مفاتيح التخزين المحلي
const ACTIVE_ACCOUNT_KEY = 'activeAccount';
const LOCAL_USER_DATA_PREFIX = 'localUserData_';
const LOCAL_CASES_PREFIX = 'localCases_';
const LOCAL_CASE_PREFIX = 'localCase_';
const LOCAL_NOTIFICATIONS_PREFIX = 'localNotifications_';

// الحصول على الحساب النشط (أونلاين أو محلي)
export const getActiveAccount = () => {
  return localStorage.getItem(ACTIVE_ACCOUNT_KEY) || 'online';
};

// تعيين الحساب النشط
export const setActiveAccount = (accountType) => {
  // التأكد من أن القيمة صحيحة
  if (accountType !== 'online' && accountType !== 'local') {
    console.error('قيمة غير صالحة للحساب النشط:', accountType);
    return;
  }

  // حفظ الحساب النشط في التخزين المحلي
  localStorage.setItem(ACTIVE_ACCOUNT_KEY, accountType);
  console.log('تم تغيير الحساب النشط إلى:', accountType);
};

// الحصول على بيانات المستخدم المحلية
export const getLocalUserData = (userId) => {
  const data = localStorage.getItem(`${LOCAL_USER_DATA_PREFIX}${userId}`);
  return data ? JSON.parse(data) : null;
};

// حفظ بيانات المستخدم المحلية
export const saveLocalUserData = (userId, userData) => {
  localStorage.setItem(`${LOCAL_USER_DATA_PREFIX}${userId}`, JSON.stringify(userData));
};

// حذف بيانات المستخدم المحلية
export const deleteLocalUserData = (userId) => {
  localStorage.removeItem(`${LOCAL_USER_DATA_PREFIX}${userId}`);
};

// الحصول على قائمة القضايا المحلية
export const getLocalCases = (userId) => {
  const data = localStorage.getItem(`${LOCAL_CASES_PREFIX}${userId}`);
  return data ? JSON.parse(data) : [];
};

// حفظ قائمة القضايا المحلية
export const saveLocalCases = (userId, cases) => {
  localStorage.setItem(`${LOCAL_CASES_PREFIX}${userId}`, JSON.stringify(cases));
};

// الحصول على بيانات قضية محلية محددة
export const getLocalCase = (userId, caseId) => {
  const data = localStorage.getItem(`${LOCAL_CASE_PREFIX}${userId}_${caseId}`);
  return data ? JSON.parse(data) : null;
};

// حفظ بيانات قضية محلية محددة
export const saveLocalCase = (userId, caseId, caseData) => {
  // حفظ القضية الفردية فقط
  localStorage.setItem(`${LOCAL_CASE_PREFIX}${userId}_${caseId}`, JSON.stringify(caseData));
};

// تحديث قائمة القضايا المحلية (يستدعى عند الحاجة فقط)
export const updateLocalCasesList = (userId, caseId, caseData) => {
  const cases = getLocalCases(userId);
  const existingIndex = cases.findIndex(c => c.id === caseId);

  if (existingIndex !== -1) {
    cases[existingIndex] = { ...caseData, id: caseId };
  } else {
    cases.push({ ...caseData, id: caseId });
  }

  saveLocalCases(userId, cases);
};

// حذف قضية محلية محددة
export const deleteLocalCase = (userId, caseId) => {
  localStorage.removeItem(`${LOCAL_CASE_PREFIX}${userId}_${caseId}`);

  // تحديث قائمة القضايا المحلية أيضًا
  const cases = getLocalCases(userId);
  const updatedCases = cases.filter(c => c.id !== caseId);
  saveLocalCases(userId, updatedCases);
};

// الحصول على الإشعارات المحلية
export const getLocalNotifications = (userId) => {
  const data = localStorage.getItem(`${LOCAL_NOTIFICATIONS_PREFIX}${userId}`);
  return data ? JSON.parse(data) : [];
};

// حفظ الإشعارات المحلية
export const saveLocalNotifications = (userId, notifications) => {
  localStorage.setItem(`${LOCAL_NOTIFICATIONS_PREFIX}${userId}`, JSON.stringify(notifications));
};

// دالة موحدة للحصول على بيانات القضايا حسب الحساب النشط
export const getCases = async (userId) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // الحصول على البيانات من Firestore
    try {
      const casesRef = collection(db, 'cases');
      const q = query(casesRef, where('userId', '==', userId));
      const querySnapshot = await getDocs(q);
      const allCases = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      // إخفاء القضايا المحولة من القائمة الرئيسية
      return allCases.filter(caseItem => !caseItem.isHidden);
    } catch (error) {
      console.error('خطأ في جلب بيانات القضايا من Firestore:', error);
      return [];
    }
  } else {
    // الحصول على البيانات من التخزين المحلي
    const allCases = getLocalCases(userId);
    // إخفاء القضايا المحولة من القائمة الرئيسية
    return allCases.filter(caseItem => !caseItem.isHidden);
  }
};

// دالة للحصول على جميع القضايا بما في ذلك المخفية (للبحث عن القضايا المرتبطة)
export const getAllCases = async (userId) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // الحصول على البيانات من Firestore
    try {
      const casesRef = collection(db, 'cases');
      const q = query(casesRef, where('userId', '==', userId));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('خطأ في جلب جميع بيانات القضايا من Firestore:', error);
      return [];
    }
  } else {
    // الحصول على البيانات من التخزين المحلي
    return getLocalCases(userId);
  }
};

// دالة موحدة للحصول على بيانات قضية محددة حسب الحساب النشط
export const getCase = async (userId, caseId) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // الحصول على البيانات من Firestore
    try {
      const caseRef = doc(db, 'cases', caseId);
      const caseDoc = await getDoc(caseRef);

      if (caseDoc.exists() && caseDoc.data().userId === userId) {
        return { id: caseDoc.id, ...caseDoc.data() };
      } else {
        return null;
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات القضية من Firestore:', error);
      return null;
    }
  } else {
    // الحصول على البيانات من التخزين المحلي
    return getLocalCase(userId, caseId);
  }
};

// دالة موحدة لإضافة قضية جديدة حسب الحساب النشط
export const addCase = async (userId, caseData) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // إضافة القضية إلى Firestore
    try {
      const casesRef = collection(db, 'cases');
      const docRef = await addDoc(casesRef, { ...caseData, userId });
      return { id: docRef.id, ...caseData };
    } catch (error) {
      console.error('خطأ في إضافة القضية إلى Firestore:', error);
      throw error;
    }
  } else {
    // إضافة القضية إلى التخزين المحلي
    const caseId = `local_${Date.now()}`;
    const newCase = { ...caseData, id: caseId, userId };
    saveLocalCase(userId, caseId, newCase);
    updateLocalCasesList(userId, caseId, newCase);
    return newCase;
  }
};

// دالة موحدة لتحديث قضية حسب الحساب النشط
export const updateCase = async (userId, caseId, caseData) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // تحديث القضية في Firestore
    try {
      const caseRef = doc(db, 'cases', caseId);
      await updateDoc(caseRef, caseData);
      return { id: caseId, ...caseData };
    } catch (error) {
      console.error('خطأ في تحديث القضية في Firestore:', error);
      throw error;
    }
  } else {
    // تحديث القضية في التخزين المحلي
    const existingCase = getLocalCase(userId, caseId);
    if (!existingCase) {
      throw new Error('القضية غير موجودة في التخزين المحلي');
    }

    const updatedCase = { ...existingCase, ...caseData };
    saveLocalCase(userId, caseId, updatedCase);

    // تحديث قائمة القضايا المحلية أيضاً
    updateLocalCasesList(userId, caseId, updatedCase);

    return updatedCase;
  }
};

// دالة محسنة لتحديث القضية مع عمليات مجمعة للأداء الأفضل
export const updateCaseOptimized = async (userId, caseId, updates) => {
  const activeAccount = getActiveAccount();

  if (activeAccount === 'online') {
    // استخدام عمليات مجمعة في Firestore للأداء الأفضل
    try {
      const batch = writeBatch(db);
      const caseRef = doc(db, 'cases', caseId);

      // إضافة العملية للمجموعة
      batch.update(caseRef, {
        ...updates,
        updatedAt: new Date().toISOString(),
      });

      // تنفيذ العمليات المجمعة
      await batch.commit();

      console.log('✅ تم تحديث القضية بنجاح باستخدام العمليات المجمعة');
      return { id: caseId, ...updates };
    } catch (error) {
      console.error('خطأ في تحديث القضية في Firestore:', error);
      throw error;
    }
  } else {
    // استخدام التحديث العادي للتخزين المحلي
    return updateCase(userId, caseId, updates);
  }
};