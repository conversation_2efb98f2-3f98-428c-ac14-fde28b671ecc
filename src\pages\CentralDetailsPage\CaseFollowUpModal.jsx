import React, { useState } from 'react';
import { FaStickyNote, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import styles from './CaseFollowUpModal.module.css';
import { getCase, updateCase, getActiveAccount } from '../../services/StorageService';

const CaseFollowUpModal = ({ onClose, onSave, caseId, userId, savedNotes = [] }) => {
  const [analysis, setAnalysis] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [loading, setLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false); // تحكم في إظهار الخانات
  const [longPressedNote, setLongPressedNote] = useState(null); // للتحكم في إظهار زر الحذف
  const [pressTimer, setPressTimer] = useState(null);

  const handleSave = async () => {
    if (!selectedType || !analysis.trim()) return;
    setLoading(true);

    try {
      const noteId = `${caseId}-note-${Date.now()}`;
      const newNote = {
        id: noteId,
        type: selectedType,
        analysis: analysis.trim(),
        isDeleted: false,
        userId: userId
      };

      const activeAccount = getActiveAccount();
      const caseData = await getCase(userId, caseId);

      if (!caseData) {
        throw new Error('لم يتم العثور على القضية');
      }

      await updateCase(userId, caseId, {
        ...caseData,
        notes: [...(caseData.notes || []), newNote],
        updatedAt: new Date().toISOString()
      });

      onSave(newNote);
      setAnalysis('');
      setSelectedType('');
    } catch (error) {
      console.error('خطأ في حفظ الملاحظة:', error);
      alert(error.message === 'لم يتم العثور على القضية'
        ? 'لم يتم العثور على القضية. يرجى التأكد من أن القضية موجودة وإعادة المحاولة.'
        : 'حدث خطأ أثناء حفظ الملاحظة. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  const renderTypeButton = (label) => (
    <button
      className={`${styles.styledButton} ${selectedType === label ? styles.activeButton : ''}`}
      onClick={() => setSelectedType(label)}
    >
      {label}
    </button>
  );

  // وظائف للتعامل مع الضغط المطول
  const handleMouseDown = (noteIndex) => {
    const timer = setTimeout(() => {
      setLongPressedNote(noteIndex);
    }, 800); // 800ms للضغط المطول
    setPressTimer(timer);
  };

  const handleMouseUp = () => {
    if (pressTimer) {
      clearTimeout(pressTimer);
      setPressTimer(null);
    }
  };

  const handleMouseLeave = () => {
    if (pressTimer) {
      clearTimeout(pressTimer);
      setPressTimer(null);
    }
  };

  // وظيفة حذف الملاحظة
  const handleDeleteNote = async (noteIndex) => {
    try {
      setLoading(true);
      const caseData = await getCase(caseId);
      if (caseData && caseData.notes) {
        const updatedNotes = caseData.notes.filter((_, index) => index !== noteIndex);
        await updateCase(caseId, { ...caseData, notes: updatedNotes });

        // تحديث الحالة المحلية
        if (onSave) {
          onSave({ notes: updatedNotes });
        }
      }
      setLongPressedNote(null);
    } catch (error) {
      console.error('خطأ في حذف الملاحظة:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.modalContent}>
      {/* Header مشابه لسجل المهام */}
      <div className={styles.notesHeader} onClick={() => setShowDetails((prev) => !prev)}>
        <div className={styles.notesHeaderLeft}>
          <FaStickyNote className={styles.notesHeaderIcon} />
          <h2 className={styles.notesHeaderTitle}>الملاحظات</h2>
        </div>
        <div className={styles.expandButton}>
          {showDetails ? <FaChevronUp /> : <FaChevronDown />}
        </div>
      </div>

      {/* المحتوى الداخلي */}
      <div className={styles.notesContent}>
        <div className={styles.notesInnerContent}>
          {/* سجل البيانات أولاً */}
          {savedNotes.length > 0 && (
            <div className={styles.savedNotesSection}>
              <h3 className={styles.textareaTitle}>سجل البيانات</h3>
              <ul className={styles.savedNotesList}>
                {savedNotes.map((note, index) => (
                  <li
                    key={index}
                    className={`${styles.savedNoteItem} ${longPressedNote === index ? styles.showDelete : ''}`}
                    onMouseDown={() => handleMouseDown(index)}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseLeave}
                    onTouchStart={() => handleMouseDown(index)}
                    onTouchEnd={handleMouseUp}
                  >
                    <div className={styles.noteHeader}>
                      <span className={styles.noteType}>{note.type}</span>
                    </div>
                    <div className={styles.noteContent}>{note.analysis}</div>
                    {longPressedNote === index && (
                      <button
                        className={styles.deleteButton}
                        onClick={() => handleDeleteNote(index)}
                        title="حذف الملاحظة"
                      >
                        ×
                      </button>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* المحتوى القابل للتوسع */}
          {showDetails && (
            <>
              <div className={styles.typeSection}>
                <h3 className={styles.textareaTitle}>اختيار نوع الملاحظة</h3>
                <div className={styles.buttonRow}>
                  {renderTypeButton('رقم صادر')}
                  {renderTypeButton('رقم وارد')}
                  {renderTypeButton('رقم إعلان')}
                  {renderTypeButton('أخرى')}
                </div>
              </div>

              <div className={styles.textareaSection}>
                <h3 className={styles.textareaTitle}>تحرير ملاحظات</h3>
                <div className={styles.textareaContainer}>
                  <textarea
                    className={styles.styledInput}
                    value={analysis}
                    onChange={(e) => setAnalysis(e.target.value)}
                    placeholder={`اكتب ملاحظة ${selectedType || ''}`}
                    disabled={!selectedType}
                  />
                </div>
              </div>

              <div className={styles.buttonGroup}>
                <button onClick={handleSave} disabled={!selectedType || !analysis.trim()}>
                  {loading ? 'جاري الحفظ...' : 'حفظ'}
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CaseFollowUpModal;
