import { FaHistory } from 'react-icons/fa';
// استخدام نفس ملف CSS للبطاقات، ولكن قد نحتاج لتمرير كلاسات إضافية
import styles from './CaseDetails.module.css';
import { cleanupHistoryEntries } from './ReportDetailsLogic';

// تعديل المكون ليقبل كلاسات إضافية ويتضمن رأس بطاقة مشابه
const CaseArchiveView = ({ caseData, cardClassName }) => {
  // دالة لتوليد الأرشيف الزمني من بيانات القضية
  const generateTimeline = () => {
    const timelineEvents = [];

    // إذا لم يكن هناك أرشيف تاريخي، أنشئ حدث واحد من بيانات القضية (للقضايا القديمة)
    if (!caseData.history || caseData.history.length === 0) {
      if (caseData.caseDate) {
        const caseDate = new Date(caseData.caseDate).toLocaleDateString('ar-EG');
        let eventDescription = '';
        if (caseData.caseStatus === 'دعوى قضائية') {
          eventDescription = `تم رفع دعوى قضائية بتاريخ ${caseDate}`;
        } else if (caseData.caseStatus === 'محضر') {
          eventDescription = `تم تحرير المحضر بتاريخ ${caseDate}`;
        } else {
          eventDescription = `تم إنشاء ${caseData.caseStatus} بتاريخ ${caseDate}`;
        }
        timelineEvents.push({ date: caseDate, event: eventDescription });
      } else if (caseData.createdAt) {
        const createdDate = new Date(caseData.createdAt).toLocaleDateString('ar-EG');
        timelineEvents.push({
          date: createdDate,
          event: `تم إنشاء ${caseData.caseStatus || 'الملف'}`
        });
      }
    }

    // تنظيف السجل التاريخي وعرض الأحداث المكتملة فقط
    if (caseData.history && caseData.history.length > 0) {
      const cleanedHistory = cleanupHistoryEntries(caseData.history);
      cleanedHistory.forEach(historyItem => {
        if (historyItem.type === 'completed_action' && historyItem.completedAt) {
          const completedDate = new Date(historyItem.completedAt).toLocaleDateString('ar-EG');
          timelineEvents.push({ date: completedDate, event: `تم تنفيذ إجراء: ${historyItem.description || 'غير محدد'}` });
        } else if (historyItem.type === 'completed_deferral' && historyItem.completedAt) {
          const sessionDate = new Date(historyItem.completedAt).toLocaleDateString('ar-EG');
          let eventText = `تم حضور جلسة: ${historyItem.description || 'غير محدد'}`;
          if (historyItem.deferralDescription && historyItem.deferralDescription.trim()) {
            eventText += ` - ${historyItem.deferralDescription}`;
          }
          timelineEvents.push({ date: sessionDate, event: eventText });
        } else if (historyItem.type === 'case_created') {
          const createdDate = new Date(historyItem.timestamp || historyItem.createdAt).toLocaleDateString('ar-EG');
          timelineEvents.push({ date: createdDate, event: historyItem.action || 'تم إنشاء القضية' });
        } else if (historyItem.type === 'status_transfer' && historyItem.timestamp) {
          // إضافة إدخالات تحويل الحالة
          const transferDate = new Date(historyItem.timestamp).toLocaleDateString('ar-EG');
          timelineEvents.push({
            date: transferDate,
            event: historyItem.action || `تم تحويل حالة الملف من "${historyItem.oldStatus}" إلى "${historyItem.newStatus}"`
          });
        } else if (historyItem.type === 'degree_transfer' && historyItem.timestamp) {
          // إضافة إدخالات تحويل الدرجة
          const transferDate = new Date(historyItem.timestamp).toLocaleDateString('ar-EG');
          timelineEvents.push({
            date: transferDate,
            event: historyItem.action || `تم تحويل الدرجة إلى ${historyItem.newCaseDegree}`
          });
        } else if (historyItem.type === 'report_created' && historyItem.timestamp) {
          // إضافة إدخالات إنشاء المحضر
          const reportDate = new Date(historyItem.timestamp).toLocaleDateString('ar-EG');
          timelineEvents.push({
            date: reportDate,
            event: historyItem.action || 'تم كتابة محضر'
          });
        } else if (historyItem.type === 'lawsuit_created' && historyItem.timestamp) {
          // إضافة إدخالات إنشاء الدعوى
          const lawsuitDate = new Date(historyItem.timestamp).toLocaleDateString('ar-EG');
          timelineEvents.push({
            date: lawsuitDate,
            event: historyItem.action || 'تم رفع دعوى قضائية'
          });
        }
      });
    }

    // إضافة أحداث التاريخ المخصص (إذا كانت موجودة)
    if (caseData.timeline && caseData.timeline.length > 0) {
      caseData.timeline.forEach(event => {
        timelineEvents.push({ date: event.date, event: event.description });
      });
    }

    // ترتيب الأحداث حسب التاريخ (الأحدث أولاً)
    timelineEvents.sort((a, b) => {
      try {
        // محاولة تحويل التواريخ مع معالجة الأخطاء المحتملة
        const dateA = new Date(a.date.split('/').reverse().join('-'));
        const dateB = new Date(b.date.split('/').reverse().join('-'));
        // التحقق من صحة التواريخ قبل المقارنة
        if (isNaN(dateA) || isNaN(dateB)) return 0;
        return dateB - dateA;
      } catch (e) {
        console.error("Error parsing date for sorting:", a.date, b.date, e);
        return 0; // إعادة 0 في حالة الخطأ لتجنب تعطل الترتيب
      }
    });

    return timelineEvents;
  };

  const timelineEvents = generateTimeline();

  if (!caseData) {
    return null; // أو عرض رسالة تحميل
  }

  return (
    // إذا تم تمرير cardClassName، استخدمه، وإلا استخدم التنسيق الافتراضي
    cardClassName ? (
      // استخدام الكلاسات الممررة لتطبيق تنسيق البطاقة
      <>
        {/* إضافة رأس بطاقة مشابه للبطاقات الأخرى */}
        <div className={styles.cardHeader}>
          <div className={styles.cardHeaderContent}>
            <div className={styles.cardIcon}><FaHistory /></div>
            <div className={styles.cardTitle}>الأرشيف الزمني</div>
          </div>
          {/* يمكن إضافة زر توسيع هنا إذا لزم الأمر لاحقاً */}
        </div>

        {/* محتوى الأرشيف - استخدام كلاسات التنسيق الموجودة */}
        <div className={styles.cardPrimaryContent}>
          {timelineEvents.length > 0 ? (
            // حاوية قابلة للتمرير إذا كان المحتوى طويلاً
            <div className={styles.timelineScrollContainer}>
              {timelineEvents.map((event, index) => (
                // استخدام كلاسات تنسيق الأرشيف الزمني من الملف الرئيسي
                <div key={index} className={styles.timelineEntry}>
                  <span className={styles.timelineDate}>{event.date}</span>
                  <span className={styles.timelineDescription}>{event.event}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.noTimeline}> {/* كلاس لرسالة عدم وجود بيانات */}
              لا توجد أحداث زمنية مسجلة
            </div>
          )}
        </div>
      </>
    ) : (
      // التنسيق الافتراضي للاستخدام المستقل
      <div className={`${styles.coloredCard} ${styles.timelineCard}`}>
        <div className={styles.cardHeader}>
          <div className={styles.cardHeaderContent}>
            <div className={styles.cardIcon}><FaHistory /></div>
            <div className={styles.cardTitle}>الأرشيف الزمني</div>
          </div>
        </div>
        <div className={styles.cardPrimaryContent}>
          {timelineEvents.length > 0 ? (
            <div className={styles.timelineScrollContainer}>
              {timelineEvents.map((event, index) => (
                <div key={index} className={styles.timelineEntry}>
                  <span className={styles.timelineDate}>{event.date}</span>
                  <span className={styles.timelineDescription}>{event.event}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.noTimeline}>
              لا توجد أحداث زمنية مسجلة
            </div>
          )}
        </div>
      </div>
    )
  );
};

export default CaseArchiveView;

