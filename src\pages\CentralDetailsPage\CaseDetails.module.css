/* متغيرات CSS مخصصة - ثيم أزرق مهني */
:root {
  /* ألوان أساسية - لوحة زرقاء مهنية */
  --primary-color: #4c68c0; /* أزرق متوسط */
  --primary-light: #dad1dc; /* لافندر فاتح */
  --primary-dark: #2a2e70; /* أزرق داكن أساسي */

  /* ألوان ثانوية */
  --secondary-color: #555269; /* رمادي-أزرق داكن */
  --secondary-light: #dad1dc; /* لافندر فاتح */
  --secondary-dark: #2a2e70; /* أزرق داكن */

  /* ألوان الحالات */
  --success-color: #4c68c0; /* أزرق للنجاح */
  --warning-color: #555269; /* رمادي-أزرق للتحذير */
  --danger-color: #2a2e70; /* أزرق داكن للخطر */

  /* ألوان البطاقات - ثيم قانوني مهني */
  --parties-color: #2a2e70; /* أزرق داكن مهني للأطراف */
  --parties-dark: #1a1d4a;
  --parties-light: #4c68c0;

  --identification-color: #555269; /* رمادي-أزرق للترقيم */
  --identification-dark: #3a3d52;
  --identification-light: #6b6f85;

  --location-color: #675f97; /* أزرق داكن للمكان */
  --location-dark: #391265;
  --location-light: #442b6c;

  /* لون بطاقة الأرشيف */
  --timeline-color: #dededf; /* لافندر فاتح للأرشيف */
  --timeline-dark: #555269;
  --timeline-light: #f0eef1;

  /* ألوان محايدة */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  /* تأثيرات ناعمة */
  --border-radius-sm: 6px;
  --border-radius: 10px;
  --border-radius-lg: 14px;
  --border-radius-xl: 18px;

  --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
  --shadow: 0 2px 4px rgba(0,0,0,0.05);
  --shadow-md: 0 4px 8px rgba(0,0,0,0.05);
  --shadow-lg: 0 6px 12px rgba(0,0,0,0.05);
  --shadow-xl: 0 8px 16px rgba(0,0,0,0.05);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* التخطيط العام */
.pageWrapper {
  min-height: 100vh;
  background: var(--neutral-50);
  padding-top: 80px;
}

.mainContainer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;

}

.headerSection {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 32px;
  box-shadow: var(--shadow);
  border: 1px solid var(--neutral-200);
  position: relative;
  overflow: hidden;
}

.headerSection::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

/* عنوان القضية */
.caseTitle {
  font-size: 2.2rem;
  font-weight: 600;
  color: var(--neutral-800);
  text-align: center;
  margin-bottom: 16px;
  line-height: 1.3;
}

/* حاوية البطاقات - تخطيط أفقي للشاشات الكبيرة (4 أعمدة) */
.coloredCardsContainerHorizontal {
  display: grid;
  grid-template-columns: 1fr; /* عمود واحد افتراضي للهواتف */
  gap: 20px;
  width: 100%;
}

/* تطبيق 4 أعمدة للشاشات الكبيرة (مثل الكمبيوتر) */
@media (min-width: 1200px) {
  .coloredCardsContainerHorizontal {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* تطبيق عمودين للشاشات المتوسطة (مثل التابلت) */
@media (min-width: 769px) and (max-width: 1199px) {
  .coloredCardsContainerHorizontal {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* البطاقات الملونة - ثيم جديد */
.coloredCard {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 15px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border:0px solid transparent;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  position: relative;
}

.coloredCard:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* إضافة إضاءة خفيفة للبطاقات عند التحويم - ثيم قانوني */
.partiesCard:hover {
  box-shadow: 0 8px 25px rgba(26, 54, 93, 0.25);
}

.identificationCard:hover {
  box-shadow: 0 8px 25px rgba(116, 66, 16, 0.25);
}

.locationCard:hover {
  box-shadow: 0 8px 25px rgba(26, 32, 44, 0.25);
}

/* ألوان البطاقات - ثيم جديد مع تدرجات */
.partiesCard {
  border-color: var(--parties-dark);
  background: linear-gradient(135deg, var(--parties-light) 0%, #ffffff 10%, var(--parties-light) 500%);
  position: relative;
  overflow: hidden;
}

.partiesCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--parties-color), var(--parties-dark));
}

.identificationCard {
  border-color: var(--identification-dark);
  background: linear-gradient(135deg, var(--identification-light) 0%, #ffffff 10%, var(--identification-light) 500%);
  position: relative;
  overflow: hidden;
}

.identificationCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--identification-color), var(--identification-dark));
}

.locationCard {
  border-color: var(--location-dark);
  background: linear-gradient(135deg, var(--location-light) 0%, #ffffff 10%, var(--location-light) 500%);
  position: relative;
  overflow: hidden;
}

.locationCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--location-color), var(--location-dark));
}

/* إضافة لمعة خفيفة للبطاقات */
.partiesCard::before,
.identificationCard::before,
.locationCard::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: rotate(45deg);
  transition: var(--transition);
  opacity: 0;
  pointer-events: none;
}

.partiesCard:hover::before,
.identificationCard:hover::before,
.locationCard:hover::before {
  opacity: 1;
  animation: shimmer 1.5s ease-in-out;
}

.timelineCard {
  border-color: var(--timeline-dark);
  background: linear-gradient(to right, var(--timeline-light), white 15%);
}

/* رأس البطاقة - ثيم جديد */
.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: none;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0px 0px 0 0;
}

.partiesCard .cardHeader {
  background: linear-gradient(135deg, var(--parties-color), var(--parties-dark));
  color: white;
  box-shadow: 0 4px 15px rgba(26, 54, 93, 0.4);
}

.identificationCard .cardHeader {
  background: linear-gradient(135deg, var(--identification-color), var(--identification-dark));
  color: white;
  box-shadow: 0 4px 15px rgba(116, 66, 16, 0.4);
}

.locationCard .cardHeader {
  background: linear-gradient(135deg, var(--location-color), var(--location-dark));
  color: white;
  box-shadow: 0 4px 15px rgba(26, 32, 44, 0.4);
}

.timelineCard .cardHeader {
  background: var(--timeline-color);
  color: var(--neutral-800);
}

.cardHeaderContent {
  display: flex;
  align-items: center;
  gap: 12px;
}

.cardIcon {
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  width: 40px;
  height: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
}

.cardTitle {
  font-weight: 600;
  font-size: 1rem;
}

.expandButton {
  font-size: 1rem;
  color: var(--neutral-600);
  transition: var(--transition);
}

.cardHeader:hover .expandButton {
  color: var(--neutral-800);
}

/* محتوى البطاقة الأساسي والموسع */
.cardPrimaryContent,
.cardExpandedContent {
  padding: 15px;
  flex-grow: 1; /* لجعل المحتوى يملأ المساحة المتاحة */
  overflow-y: auto; /* إضافة تمرير إذا لزم الأمر */
}

.cardExpandedContent {
  background: var(--neutral-50);
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(-10px);
}

.cardExpandedContent.expanded {
  max-height: 500px; /* ارتفاع كافي للمحتوى */
  opacity: 1;
  transform: translateY(0);
}

.expandedContentDivider {
  height: 1px;
  background: var(--neutral-200);
  margin-bottom: 15px;
  opacity: 0;
  transition: opacity 0.3s ease 0.1s;
}

.cardExpandedContent.expanded .expandedContentDivider {
  opacity: 1;
}

/* حقول البيانات - تخطيط أفقي */
.dataFieldHorizontal {
  display: flex;
  align-items: flex-start; /* محاذاة للبداية */
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed var(--neutral-200);
  gap: 10px; /* مسافة بين العنوان والقيمة */
}

/* انيميشن فقط للحقول في المحتوى الموسع */
.cardExpandedContent .dataFieldHorizontal {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.3s ease;
}

.cardExpandedContent.expanded .dataFieldHorizontal {
  opacity: 1;
  transform: translateX(0);
}

/* تأخير متدرج للحقول في المحتوى الموسع */
.cardExpandedContent.expanded .dataFieldHorizontal:nth-child(2) { transition-delay: 0.1s; }
.cardExpandedContent.expanded .dataFieldHorizontal:nth-child(3) { transition-delay: 0.2s; }
.cardExpandedContent.expanded .dataFieldHorizontal:nth-child(4) { transition-delay: 0.3s; }
.cardExpandedContent.expanded .dataFieldHorizontal:nth-child(5) { transition-delay: 0.4s; }

/* الحقول في المحتوى الأساسي تبقى مرئية دائماً */
.cardPrimaryContent .dataFieldHorizontal {
  opacity: 1;
  transform: translateX(0);
}

.dataFieldHorizontal:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.fieldLabelHorizontal {
  font-size: 0.9rem;
  color: var(--neutral-600);
  white-space: nowrap; /* منع التفاف العنوان */
  flex-shrink: 0; /* منع العنوان من الانكماش */
}

.fieldValueContainerHorizontal {
  flex-grow: 1; /* جعل حاوية القيمة تأخذ المساحة المتبقية */
  text-align: left; /* محاذاة القيمة لليسار */
}

.valueWithActionHorizontal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 8px; /* مسافة بين القيمة وزر التعديل */
}

.valueTextHorizontal {
  color: var(--neutral-800);
  font-size: 0.95rem;
  font-weight: 500;
  word-break: break-word; /* السماح بكسر الكلمات الطويلة */
}

/* مدخلات الأرشيف الزمني */
.timelineScrollContainer {
  max-height: 300px; /* تحديد ارتفاع أقصى للأرشيف */
  overflow-y: auto; /* إضافة تمرير عمودي إذا تجاوز المحتوى الارتفاع */
  padding-right: 5px; /* مسافة صغيرة لشريط التمرير */
}

.timelineEntry {
  display: flex;
  flex-direction: column; /* تاريخ فوق الوصف */
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed var(--neutral-200);
}

.timelineEntry:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.timelineDate {
  font-size: 0.85rem;
  color: var(--neutral-600);
  margin-bottom: 4px;
  font-weight: 500;
}

.timelineDescription {
  font-size: 0.95rem;
  color: var(--neutral-800);
  word-break: break-word; /* كسر الكلمات الطويلة في الوصف */
}

/* رسالة عدم وجود بيانات في الأرشيف */
.noTimeline {
  text-align: center;
  color: var(--neutral-500);
  font-size: 0.9rem;
  padding: 20px 0;
}

/* قيم منطقية */
.booleanValue {
  display: inline-flex; /* تغيير لـ inline-flex */
  align-items: center;
  gap: 6px;
  padding: 3px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  font-weight: 500;
}

.trueValue {
  background: rgba(127, 182, 133, 0.2);
  color: #2e7d32;
}

.falseValue {
  background: rgba(226, 143, 131, 0.2);
  color: #c62828;
}

/* زر التعديل الأفقي */
.editIconButtonHorizontal {
  width: 24px;
  height: 24px;
  padding: 0; /* تأكد من عدم وجود padding */
  border: none;
  border-radius: 50%;
  background: var(--neutral-100);
  color: var(--neutral-600);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  flex-shrink: 0;
  box-sizing: border-box; /* مهم جداً */
}


.editIconButtonHorizontal:hover {
  background: var(--primary-dark);
  color: white;
  opacity: 1;
}

/* زر الإحالة الأفقي */
.referralButtonHorizontal {
  width: 24px;
  height: 24px;
  padding: 0;
  border: none;
  border-radius: 50%;
  background: var(--success-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  flex-shrink: 0;
  box-sizing: border-box;
  margin-left: 4px;
}

.referralButtonHorizontal:hover {
  background: var(--success-dark);
  opacity: 1;
  transform: translateY(-1px);
}

/* مجموعة الأزرار */
.buttonGroup {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* مكونات الإحالة داخل الكارت */
.inlineInput {
  flex: 1;
  padding: 6px 8px;
  border: 2px solid var(--primary-color);
  border-radius: 6px;
  font-size: 13px;
  font-family: inherit;
  background: white;
  transition: all 0.2s ease;
}

.inlineInput:focus {
  outline: none;
  border-color: var(--success-color);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.inlineSelect {
  flex: 1;
  padding: 6px 8px;
  border: 2px solid var(--primary-color);
  border-radius: 6px;
  font-size: 13px;
  font-family: inherit;
  background: white;
  transition: all 0.2s ease;
}

.inlineSelect:focus {
  outline: none;
  border-color: var(--success-color);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.transferErrorMessage {
  background: #ffebee;
  color: #c62828;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  margin-top: 8px;
  border-left: 4px solid #f44336;
  white-space: pre-line;
}

.transferButtonGroup {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.confirmReferralButton {
  background: var(--success-color) !important;
  color: white !important;
  border: none !important;
}

.confirmReferralButton:hover {
  background: var(--success-dark) !important;
}

.confirmReferralButton:disabled {
  background: #ccc !important;
  cursor: not-allowed !important;
}

.cancelReferralButton {
  background: #f44336 !important;
  color: white !important;
  border: none !important;
}

.cancelReferralButton:hover {
  background: #d32f2f !important;
}

.closeButton {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

/* الأزرار الدائرية */
.circularButton {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.circularButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.circularButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.yesButton {
  background: var(--success-color);
  color: white;
}

.yesButton:hover:not(:disabled) {
  background: var(--success-dark);
}

.noButton {
  background: #f44336;
  color: white;
}

.noButton:hover:not(:disabled) {
  background: #d32f2f;
}

.confirmButton {
  background: var(--success-color);
  color: white;
}

.confirmButton:hover:not(:disabled) {
  background: var(--success-dark);
}

.cancelButton {
  background: #f44336;
  color: white;
}

.cancelButton:hover:not(:disabled) {
  background: #d32f2f;
}

.circularCloseButton {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 10px;
}

.circularCloseButton:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.circularButtonGroup {
  display: flex;
  gap: 6px;
  justify-content: center;
  margin-top: 8px;
}

.confirmationQuestion {
  text-align: center;
  font-size: 12px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 500;
}

/* زر التوسيع */
.expandToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  background: var(--neutral-100);
  color: var(--neutral-600);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-top: 1px solid var(--neutral-200);
  margin-top: auto; /* دفع الزر لأسفل البطاقة */
  flex-shrink: 0; /* منع الزر من الانكماش */
  position: relative;
  overflow: hidden;
}

.expandToggle:hover {
  background: var(--neutral-200);
  color: var(--neutral-800);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.expandToggle:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* انيميشن الأيقونة */
.expandToggle svg {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.expandToggle:hover svg {
  transform: scale(1.1);
}

/* أنماط خاصة لحقول رقم القضية الكامل */
.fullCaseNumberEditContainer {
  width: 100%;
}

.caseNumberFieldsContainer {
  display: flex;
  flex-direction: row; /* جعل الحقول جنب بعض */
  gap: 15px;
  margin-bottom: 15px;
  padding: 15px;
  background: var(--neutral-50);
  border-radius: var(--border-radius);
  border: 2px solid var(--primary-light);
}

/* للشاشات الصغيرة، اجعل الحقول فوق بعض */
@media (max-width: 768px) {
  .caseNumberFieldsContainer {
    flex-direction: column;
    gap: 12px;
  }
}

.caseNumberField {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1; /* جعل كل حقل يأخذ نفس المساحة */
}

.inlineLabel {
  font-size: 0.9rem;
  color: var(--neutral-700);
  font-weight: 500;
  margin-bottom: 4px;
}

/* تحسين أنماط حقول الإدخال */
.editInput {
  padding: 10px 12px;
  border: 2px solid var(--neutral-300);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: var(--transition);
  background: white;
  color: var(--neutral-800);
}

.editInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
}

.editInput.inputError {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(42, 46, 112, 0.1);
}

/* أنماط أزرار التعديل */
.editActionsHorizontal {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  justify-content: flex-end;
}

.saveEditButton,
.cancelEditButton {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.saveEditButton {
  background: var(--primary-color);
  color: white;
}

.saveEditButton:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.saveEditButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancelEditButton {
  background: var(--neutral-400);
  color: white;
}

.cancelEditButton:hover:not(:disabled) {
  background: var(--neutral-600);
  transform: translateY(-1px);
}

/* رسائل الخطأ */
.errorText {
  color: var(--danger-color);
  font-size: 0.85rem;
  margin-top: 6px;
  display: block;
  font-weight: 500;
}

/* أنماط مكون تحويل الدرجة */
.transferDegreeSection {
  margin-top: 8px;
  padding-top: 0;
  border-top: none;
}

.transferDegreeBox {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  font-size: 0.85rem;
  box-shadow:
    0 2px 6px rgba(76, 104, 192, 0.15),
    0 0 20px rgba(76, 104, 192, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* ثيم خاص لكارت الأطراف */
.transferDegreeBox.partiesTheme {
  background: linear-gradient(135deg, #f0f2ff, #e8ebff);
  border: 2px solid var(--parties-color);
  box-shadow:
    0 2px 6px rgba(42, 46, 112, 0.15),
    0 0 20px rgba(42, 46, 112, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.transferDegreeBox.partiesTheme .transferHeader {
  background: linear-gradient(135deg, var(--parties-color), var(--parties-dark));
}

.transferDegreeBox.partiesTheme .transferButton {
  background: linear-gradient(135deg, var(--parties-color), var(--parties-dark));
  box-shadow: 0 2px 4px rgba(42, 46, 112, 0.2);
}

/* ثيم خاص لكارت الترقيم والتصنيف */
.transferDegreeBox.identificationTheme {
  background: linear-gradient(135deg, #f0f4ff, #e6efff);
  border: 2px solid var(--identification-color);
  box-shadow:
    0 2px 6px rgba(76, 104, 192, 0.15),
    0 0 20px rgba(76, 104, 192, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.transferDegreeBox.identificationTheme .transferHeader {
  background: linear-gradient(135deg, var(--identification-color), var(--identification-dark));
}

.transferDegreeBox.identificationTheme .transferButton {
  background: linear-gradient(135deg, var(--identification-color), var(--identification-dark));
  box-shadow: 0 2px 4px rgba(76, 104, 192, 0.2);
}

.transferDegreeBox.identificationTheme .transferButton:hover {
  background: linear-gradient(135deg, var(--identification-dark), var(--identification-color));
  box-shadow: 0 4px 8px rgba(76, 104, 192, 0.3);
}

/* ثيم خاص لكارت المحكمة */
.transferDegreeBox.locationTheme {
  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  border: 2px solid var(--location-color);
  box-shadow:
    0 2px 6px rgba(76, 104, 192, 0.15),
    0 0 20px rgba(76, 104, 192, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.transferDegreeBox.locationTheme .transferHeader {
  background: linear-gradient(135deg, var(--location-color), var(--location-dark));
}

/* حاوية أزرار الإحالة */
.referralButtonsContainer {
  display: flex;
  gap: 10px;
  flex-direction: column;
}

@media (min-width: 768px) {
  .referralButtonsContainer {
    flex-direction: row;
  }
}

/* أزرار الإحالة */
.referralButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  min-height: 40px;
}

.courtReferralButton {
  background: linear-gradient(135deg, #4c68c0, #2a2e70);
  color: white;
  box-shadow: 0 2px 4px rgba(76, 104, 192, 0.2);
}

.courtReferralButton:hover {
  background: linear-gradient(135deg, #2a2e70, #4c68c0);
  box-shadow: 0 4px 8px rgba(76, 104, 192, 0.3);
  transform: translateY(-1px);
}

.expertReferralButton {
  background: linear-gradient(135deg, #555269, #2a2e70);
  color: white;
  box-shadow: 0 2px 4px rgba(85, 82, 105, 0.2);
}

.expertReferralButton:hover {
  background: linear-gradient(135deg, #2a2e70, #555269);
  box-shadow: 0 4px 8px rgba(85, 82, 105, 0.3);
  transform: translateY(-1px);
}

/* تم إزالة انيميشن shimmer لتجنب الاهتزاز */

/* أنماط نافذة تحويل الحالة المحسنة */
.statusOptionsContainer {
  margin: 20px 0;
  padding: 15px;
  background: var(--neutral-50);
  border-radius: var(--border-radius);
  border: 1px solid var(--neutral-200);
}

.questionText {
  font-size: 1rem;
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: 15px;
  text-align: center;
}

.statusOptions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.statusOptionButton {
  padding: 12px 20px;
  border: 2px solid var(--neutral-300);
  border-radius: var(--border-radius);
  background: white;
  color: var(--neutral-700);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.statusOptionButton:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(76, 104, 192, 0.2);
}

.statusOptionButton.selected {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(76, 104, 192, 0.3);
}

.statusOptionButton.selected:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
}

.dateInputContainer {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: var(--border-radius);
  border: 1px solid var(--neutral-300);
}

.dateLabel {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: 8px;
}

.dateInput {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid var(--neutral-300);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: var(--transition);
  background: white;
  color: var(--neutral-800);
}

.dateInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
}

/* تحسين أزرار التأكيد */
.transferConfirmButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--neutral-400);
  border-color: var(--neutral-400);
}

.transferConfirmButton:disabled:hover {
  transform: none;
  box-shadow: none;
}

.transferHeader {
  padding: 6px 10px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transferHeader:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.transferTitleSection {
  display: flex;
  align-items: center;
  gap: 6px;
}

.expandButton {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.expandButton:hover {
  transform: scale(1.1);
}

.transferIcon {
  color: white;
  font-size: 0.9rem;
}

.transferTitle {
  font-weight: 600;
  color: white;
  font-size: 0.85rem;
}

.transferContent {
  padding: 8px 10px;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.transferInfoRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 0.8rem;
  padding: 3px 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.transferInfoRow:last-child {
  margin-bottom: 0;
}

.transferLabel {
  color: var(--neutral-600);
  font-weight: 500;
}

.transferValue {
  color: var(--primary-dark);
  font-weight: 600;
}

.transferButton {
  width: 100%;
  padding: 8px 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 4px rgba(76, 104, 192, 0.2);
}

.transferButton:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 104, 192, 0.3);
}

.transferButton:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* أنماط نافذة تأكيد تحويل الدرجة */
.transferConfirmationOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.transferConfirmationDialog {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  text-align: center;
  animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.transferConfirmationIcon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(76, 104, 192, 0.3);
}

.transferConfirmationTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: 12px;
}

.transferConfirmationMessage {
  color: var(--neutral-600);
  line-height: 1.5;
  margin-bottom: 24px;
  font-size: 0.95rem;
}

.transferConfirmationActions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.transferConfirmButton,
.transferCancelButton {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 120px;
  justify-content: center;
}

.transferConfirmButton {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  box-shadow: 0 2px 4px rgba(76, 104, 192, 0.2);
}

.transferConfirmButton:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 104, 192, 0.3);
}

.transferCancelButton {
  background: var(--neutral-400);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.transferCancelButton:hover {
  background: var(--neutral-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.partiesCard .expandToggle:hover {
  color: var(--parties-dark);
}

.identificationCard .expandToggle:hover {
  color: var(--identification-dark);
}

.locationCard .expandToggle:hover {
  color: var(--location-dark);
}

/* حاوية حقل التعديل الأفقي */
.editFieldContainerHorizontal {
  display: flex;
  flex-direction: column; /* الأزرار تحت الحقل */
  gap: 8px;
  width: 100%;
}

/* حقل الإدخال */
.editInput, .textareaInput {
  padding: 8px 10px;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
  transition: var(--transition);
  background: var(--neutral-50);
  width: 100%;
}

.editInput:focus, .textareaInput:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 2px rgba(120, 166, 200, 0.1);
}

.textareaInput {
  min-height: 60px;
  resize: vertical;
}

/* حقل خطأ */
.inputError {
  border-color: var(--danger-color);
  background: #fff5f5;
}

/* نص الخطأ */
.errorText {
  color: var(--danger-color);
  font-size: 0.85rem;
  font-weight: 400;
}

/* أزرار التعديل الأفقي */
.editActionsHorizontal {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* زر الحفظ */
.saveEditButton {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: var(--success-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.saveEditButton:hover {
  background: #3a5ba0;
}

/* زر الإلغاء */
.cancelEditButton {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: var(--danger-color);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancelEditButton:hover {
  background: #1f2356;
}

/* تأثيرات خاصة */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* تأثير اللمعة */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* تنسيقات للهواتف المحمولة (أقل من 769px) */
@media (max-width: 768px) {
  .coloredCardsContainerHorizontal {
    grid-template-columns: 1fr; /* عمود واحد للهواتف */
  }

  /* الحفاظ على التخطيط الأفقي للحقول على الهواتف */
  .dataFieldHorizontal {
    flex-direction: row; /* الحفاظ على التخطيط الأفقي */
    align-items: flex-start;
    gap: 8px; /* مسافة أصغر بين العنوان والقيمة */
  }

  .fieldLabelHorizontal {
    margin-bottom: 0; /* إزالة الهامش السفلي */
    white-space: nowrap; /* منع التفاف العنوان */
    min-width: 80px; /* عرض أدنى للعنوان */
    flex-shrink: 0;
  }

  .fieldValueContainerHorizontal {
    flex: 1; /* أخذ المساحة المتبقية */
    text-align: right; /* محاذاة لليمين على الهواتف */
  }
}

/* الحفاظ على تنسيقات الصفحات الأخرى */
.actionsSection {
  margin-top: 20px;
}

.addOptions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
}

.addDeferralButton,
.addActionOptionButton {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0), inset 0 0 10px rgba(255, 201, 135, 0.454);
}

.addDeferralButton {
  background: linear-gradient(90deg, rgba(42, 46, 112, 0.8), #2a2e70 100%);
  color: white;
}

.addDeferralButton:hover {
  background-color: #1a1d4a;
}

.addActionOptionButton {
  background: linear-gradient(90deg, rgba(76, 104, 192, 0.8), #4c68c0 100%);
  color: white;
}

.addActionOptionButton:hover {
  background-color: #3a5ba0;
}

.promptDialog {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  max-width: 450px;
  margin: 15px auto;
  text-align: center;
  direction: rtl;
}

.promptDialog h3 {
  font-size: 1.4rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 500;
}

.previewDialog {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
}
