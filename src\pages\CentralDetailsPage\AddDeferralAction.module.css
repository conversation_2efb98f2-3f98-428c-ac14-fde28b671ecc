/* أنماط واجهة إضافة التأجيلات والإجراءات فقط */
.addReportForm {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 3px solid #e2e8f0;
  margin: 20px auto;
  width: 100%;
  max-width: 900px;
  transition: all 0.3s ease;
  position: relative;
}
.dateReasonSection {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin: 16px 0;
  align-items: flex-start;
  width: 100%;
  justify-content: space-between;
}
.dateField {
  flex: 1;
  min-width: 200px;
  max-width: 48%;
}
.dateField label {
  font-weight: 600;
  color: var(--neutral-700, #374151);
  margin-bottom: 12px;
  display: block;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.dateInput {
  width: 100%;
  padding: 16px;
  border: 3px solid #d1d5db;
  border-radius: 12px;
  font-size: 16px;
  background: #ffffff;
  color: #1f2937;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
.dateInput:focus {
  outline: none;
  border-color: #2563eb;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}
.reasonSection {
  flex: 2;
  min-width: 250px;
  max-width: 48%;
}
.reasonSection label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: block;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.optionalLabel {
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: block;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.optional {
  color: #6b7280;
  font-weight: 500;
  font-size: 0.9rem;
  margin-right: 8px;
  text-transform: none;
  letter-spacing: normal;
}
.descriptionSection {
  margin: 24px 0;
  width: 100%;
}
.descriptionTextarea {
  width: 100%;
  padding: 16px;
  border: 3px solid #d1d5db;
  border-radius: 12px;
  font-size: 16px;
  background: #ffffff;
  color: #1f2937;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  line-height: 1.5;
}
.descriptionTextarea:focus {
  outline: none;
  border-color: #2563eb;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}
.descriptionTextarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}
.selectedReasons {
  padding: 16px;
  margin-bottom: 20px;
  border: 3px solid #d1d5db;
  border-radius: 12px;
  background: #ffffff;
  min-height: 60px;
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #1f2937;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  font-weight: 500;
}
.selectedReasons:focus-within {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}
.noSelection {
  color: #9ca3af;
  font-style: italic;
  font-weight: 400;
}
.reasonButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-top: 8px;
}
.reasonButton {
  padding: 12px 18px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
  font-weight: 600;
  text-align: center;
  width: fit-content;
  white-space: nowrap;
  background: #ffffff;
  color: #2563eb;
  border: 3px solid #2563eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.reasonButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.2);
  background: #f0f7ff;
  border-color: #1d4ed8;
}
.reasonButton.selected {
  background: #ffffff;
  color: #059669;
  border: 3px solid #059669;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
}
.reasonButton.selected:hover {
  background: #f0fdf4;
  border-color: #047857;
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
}
.reportFormButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 3px solid #e2e8f0;
  flex-wrap: nowrap;
  align-items: center;
}
.saveButton {
  background: #2563eb;
  color: #ffffff;
  border: 3px solid #2563eb;
  padding: 16px 28px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
  flex: 1;
  justify-content: center;
  min-width: 200px;
  min-height: 56px;
}
.saveButton:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}
.addActionForm {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 3px solid #e2e8f0;
  margin: 20px auto;
  width: 100%;
  max-width: 900px;
  transition: all 0.3s ease;
  position: relative;
}

.addActionForm::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 20px 20px 0 0;
}

.formHeader {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 3px solid #e2e8f0;
}

.formTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 12px;
}

.formSubtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
  line-height: 1.6;
}

.errorAlert {
  background: #fef2f2;
  border: 3px solid #fecaca;
  border-radius: 12px;
  padding: 16px 20px;
  margin-bottom: 24px;
  color: #dc2626;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
}

.errorAlert::before {
  content: '⚠️';
  font-size: 20px;
}

.actionField {
  margin-bottom: 28px;
}

.actionField label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: block;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.actionInput {
  width: 100%;
  padding: 16px;
  border: 3px solid #d1d5db;
  border-radius: 12px;
  font-size: 16px;
  background: #ffffff;
  color: #1f2937;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.actionInput:focus {
  outline: none;
  border-color: #2563eb;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

.actionInput:disabled {
  background: #f9fafb;
  color: #64748b;
  cursor: not-allowed;
  border-color: #d1d5db;
  border-width: 3px;
}

.actionFormButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 3px solid #e2e8f0;
  flex-wrap: nowrap;
  align-items: center;
}

.addActionButton {
  padding: 16px 28px;
  background: #10b981;
  color: white;
  border: 3px solid #10b981;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
  flex: 1;
  justify-content: center;
  min-width: 200px;
  min-height: 56px;
}

.addActionButton:hover {
  background: #059669;
  border-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.addActionButton:disabled {
  background: var(--neutral-400, #9ca3af);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancelButton {
  padding: 16px 28px;
  background: #ef4444;
  color: white;
  border: 3px solid #ef4444;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
  flex: 1;
  justify-content: center;
  min-width: 200px;
  min-height: 56px;
}

.cancelButton:hover {
  background: #dc2626;
  border-color: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}
.buttonIcon {
  margin-left: 8px;
  font-size: 18px;
  vertical-align: middle;
}

@media (max-width: 768px) {
  .addReportForm,
  .addActionForm {
    padding: 24px;
    max-width: 100%;
    margin-top: 20px;
  }
  .dateReasonSection {
    gap: 16px;
    flex-direction: column;
  }
  .dateField,
  .reasonSection {
    min-width: 100%;
    max-width: 100%;
  }
  .actionField {
    margin-bottom: 20px;
  }
  .reportFormButtons,
  .actionFormButtons {
    gap: 16px;
    flex-wrap: nowrap;
  }
  .addActionButton,
  .cancelButton,
  .saveButton {
    flex: 1;
    min-width: 0;
    max-width: none;
    padding: 14px 16px;
    font-size: 15px;
    height: 52px;
    min-height: 52px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .addReportForm,
  .addActionForm {
    padding: 16px;
  }
  .dateInput,
  .actionInput {
    padding: 12px;
    font-size: 14px;
  }
  .selectedReasons {
    padding: 12px;
    font-size: 14px;
    min-height: 48px;
  }
  .reasonButton {
    padding: 8px 12px;
    font-size: 13px;
    border-width: 2px;
  }
  .reportFormButtons,
  .actionFormButtons {
    gap: 12px;
    flex-wrap: nowrap;
  }
  .addActionButton,
  .cancelButton,
  .saveButton {
    flex: 1;
    min-width: 0;
    max-width: none;
    padding: 12px 8px;
    font-size: 14px;
    height: 48px;
    min-height: 48px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}
