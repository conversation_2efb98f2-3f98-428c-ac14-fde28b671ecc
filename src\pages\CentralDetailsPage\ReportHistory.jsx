import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { FaTrashAlt, FaHistory, FaLink, FaCheck, FaCalendarAlt, FaClock } from 'react-icons/fa';
import styles from "./ReportHistory.module.css";
import { onSnapshot, doc } from 'firebase/firestore';
import { db } from '../../config/firebaseConfig';
import { handleStrikeAction, handleDeleteReport, handleCompleteAction, handleCompleteDeferral, handleAddAction } from './ReportDetailsLogic';
import { getActiveAccount, getCase, updateCase } from '../../services/StorageService';
import { notifyTaskCreated } from '../../utils/CacheManager';
import AddDeferral from './AddDeferral';
import AddAction from './AddAction';

const ReportHistory = ({ currentUser }) => {
  const { caseNumber } = useParams();
  const decodedCaseNumber = decodeURIComponent(caseNumber || '');
  const [caseItem, setCaseItem] = useState(null);
  const [deferrals, setDeferrals] = useState([]);
  const [actions, setActions] = useState([]);
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(null);

  const loadCaseData = async () => {
    if (!currentUser || !decodedCaseNumber) {
      setError('المستخدم أو رقم القضية غير متوفر. يرجى تسجيل الدخول أو التحقق من رقم القضية.');
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);

    try {
      const activeAccount = getActiveAccount();
      const caseData = await getCase(currentUser.uid, decodedCaseNumber);

      if (caseData) {
        setCaseItem(caseData);

        if (activeAccount === 'online') {
          const caseRef = doc(db, 'cases', caseData.id);
          const unsubscribe = onSnapshot(caseRef, async (docSnapshot) => {
            if (!docSnapshot.exists()) {
              setError('القضية غير موجودة.');
              setLoading(false);
              return;
            }

            const updatedCaseData = { id: docSnapshot.id, ...docSnapshot.data() };
            processAndSetCaseData(updatedCaseData);
            setLoading(false);
          }, (error) => {
            setError('خطأ في تحديث البيانات: ' + error.message);
            setLoading(false);
          });
          return () => unsubscribe();
        } else {
          processAndSetCaseData(caseData);
          setLoading(false);
        }
      } else {
        setError(activeAccount === 'online'
          ? 'لم يتم العثور على بيانات القضية في الحساب الأونلاين أو ليس لديك إذن للوصول إليها.'
          : 'لم يتم العثور على بيانات القضية في الحساب المحلي.');
        setLoading(false);
      }
    } catch (e) {
      console.error('خطأ في جلب بيانات القضية:', e);
      setError('خطأ في جلب بيانات القضية: ' + e.message);
      setLoading(false);
    }
  };

  const processAndSetCaseData = (caseData) => {
    const deferralsData = (caseData.deferrals || []).filter(d => !d.isDeleted);
    const actionsData = (caseData.actions || []).filter(a => !a.isDeleted);
    const historyData = caseData.history || [];

    setCaseItem(caseData);
    setDeferrals(deferralsData);
    setActions(actionsData);
    setHistory(historyData);
  };

  useEffect(() => {
    loadCaseData();
  }, [decodedCaseNumber, currentUser]);

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const renderItemContent = (item) => {
    return (
      <div className={styles.itemContent}>
        <div className={styles.itemDate}>
          <FaCalendarAlt className={styles.dateIcon} />
          {formatDate(item.type === 'deferral' ? item.data.date : item.data.deadline)}
        </div>
        <div className={styles.itemMain}>
          <div className={styles.itemText}>
            {item.type === 'deferral' ? (
              <>
                {item.data.reasons?.join('، ') || 'تأجيل بدون تفاصيل'}
                {item.data.description && ` - ${item.data.description}`}
              </>
            ) : (
              <>
                {item.data.description}
                {item.data.linkedDeferralId && (
                  <span className={styles.linkIndicator}>
                    <FaLink className={styles.linkIcon} />
                    مرتبط بتأجيل
                  </span>
                )}
              </>
            )}
          </div>
        </div>
        <div className={styles.itemActions}>
          <button
            onClick={() => item.type === 'deferral'
              ? handleCompleteDeferral(item.index, deferrals, setDeferrals, caseItem, setHistory)
              : handleCompleteAction(item.data.id || `action-${item.index}`, actions, setActions, caseItem, setHistory)
            }
            className={styles.completeButton}
            title={item.type === 'deferral' ? "تم الحضور" : "تم الإنجاز"}
          >
            <FaCheck />
          </button>
          <button
            onClick={() => item.type === 'deferral'
              ? handleDeleteReport(item.index, deferrals, setDeferrals, history, setHistory, caseItem, actions, setActions)
              : handleStrikeAction(item.data.id || `action-${item.index}`, actions, setActions, caseItem, setHistory)
            }
            className={styles.deleteButton}
            title="حذف"
          >
            <FaTrashAlt />
          </button>
        </div>
      </div>
    );
  };

  const renderCombinedItems = () => {
    const allItems = [
      ...deferrals.map(deferral => ({
        type: 'deferral',
        data: deferral,
        date: new Date(deferral.date || new Date()),
        index: deferrals.indexOf(deferral)
      })),
      ...actions.map(action => ({
        type: 'action',
        data: action,
        date: new Date(action.deadline || new Date()),
        index: actions.indexOf(action)
      }))
    ].sort((a, b) => b.date - a.date);

    const hasItems = allItems.length > 0;

    return (
      <div className={styles.historyContainer}>
        <div className={styles.historyHeader}>
          <FaHistory className={styles.headerIcon} />
          <h2 className={styles.headerTitle}>سجل المهام</h2>
        </div>
        <div className={styles.historyContent}>
          <div className={styles.itemsList}>
            {hasItems ? (
              allItems.map((item, index) => (
                <div
                  key={index}
                  className={`${styles.historyItem} ${item.type === 'action' ? styles.actionItem : styles.deferralItem}`}
                >
                  {renderItemContent(item)}
                </div>
              ))
            ) : (
              <div className={styles.noReports}>
                لا توجد مهام مسجلة
              </div>
            )}
            {/* قسم إضافة المهام الجديدة */}
            <div className={`${styles.historyItem} ${styles.actionItem}`}>
              <div className={styles.itemContent}>
                <div className={styles.itemMain}>
                  {!showForm ? (
                    <>
                      <button
                        onClick={() => setShowForm('deferral')}
                        className={styles.actionButton}
                      >
                        <FaCalendarAlt className={styles.buttonIcon} />
                        إضافة تنبيه بتاريخ جلسة
                      </button>
                      <button
                        onClick={() => setShowForm('action')}
                        className={styles.actionButton}
                      >
                        <FaClock className={styles.buttonIcon} />
                        إضافة تنبيه بإجراء
                      </button>
                    </>
                  ) : showForm === 'deferral' ? (
                    <AddDeferral
                      currentUser={currentUser}
                      caseItem={caseItem}
                      onClose={() => setShowForm(null)}
                      onSave={handleSaveDeferral}
                      isUnderConsideration={caseItem?.caseStatus === 'قيد النظر'}
                    />
                  ) : showForm === 'action' ? (
                    <AddAction
                      currentUser={currentUser}
                      caseItem={caseItem}
                      deferrals={deferrals}
                      actions={actions}
                      onClose={() => setShowForm(null)}
                      onSave={handleSaveAction}
                    />
                  ) : null}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const handleSaveDeferral = async (reportDate, selectedReasons, deferralDescription, setError) => {
    try {
      if (!reportDate || isNaN(new Date(reportDate).getTime()) || selectedReasons.length === 0) {
        setError("يرجى إدخال تاريخ التأجيلة واختيار سبب واحد على الأقل");
        return;
      }

      const newDeferral = {
        id: `${caseItem.id}-defer-${Date.now()}`,
        date: reportDate,
        reasons: selectedReasons,
        description: deferralDescription || '',
        createdAt: new Date().toISOString(),
        isDeleted: false,
        isArchived: false
      };

      const updatedDeferrals = [...deferrals, newDeferral];
      setDeferrals(updatedDeferrals);

      await updateCase(currentUser.uid, caseItem.id, {
        deferrals: updatedDeferrals,
      });

      alert('تم إضافة التنبيه بتاريخ الجلسة بنجاح');
      notifyTaskCreated(currentUser.uid);
      setShowForm(null);

    } catch (err) {
      console.error("Error in handleSaveDeferral:", err);
      setError(err.message);
    }
  };

  const handleSaveAction = async (newAction, actionDeadline, linkType, linkedDeferralId, linkedActionId, reminderType, setError) => {
    try {
      const updatedCaseData = { ...caseItem, deferrals };
      await handleAddAction(
        newAction,
        actionDeadline,
        linkType,
        linkedDeferralId,
        linkedActionId,
        reminderType,
        updatedCaseData,
        actions,
        setActions,
        () => {},
        () => {},
        () => {},
        () => {},
        () => {},
        () => {},
        setHistory
      );

      notifyTaskCreated(currentUser.uid);
      setShowForm(null);
    } catch (err) {
      console.error("Error in handleSaveAction:", err);
      setError(err.message);
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingState}>
        <div className={styles.loadingSpinner}></div>
        <p className={styles.loadingText}>جاري تحميل البيانات...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorState}>
        <div className={styles.errorIcon}>!</div>
        <p className={styles.errorText}>{error}</p>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {renderCombinedItems()}
    </div>
  );
};

export default ReportHistory;