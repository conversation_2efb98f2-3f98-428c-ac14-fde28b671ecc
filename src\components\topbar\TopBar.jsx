import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>ell, Fi<PERSON>ser, FiSearch, FiLogOut, FiPlusCircle, FiHome, FiSettings, FiUsers } from 'react-icons/fi';
import { FaGlobe, FaMobileAlt } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { auth, db } from "../../config/firebaseConfig";
import { signOut } from "firebase/auth";
import { collection, query, where, getDocs } from "firebase/firestore";
import styles from './TopBar.module.css';
import { getActiveAccount, setActiveAccount } from '../../services/StorageService';

const debounce = (func, wait) => {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

const fetchNotifications = async (userId) => {
  if (!userId) return [];
  const casesQuery = query(
    collection(db, 'cases'),
    where('userId', '==', userId)
  );
  const querySnapshot = await getDocs(casesQuery);
  const userCases = querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  }));

  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  const generatedNotifications = [];

  userCases.forEach((caseItem) => {
    const deferrals = caseItem.deferrals || [];
    deferrals.forEach((deferral, index) => {
      if (deferral.isDeleted || !deferral.date) return;
      const date = new Date(deferral.date);
      if (
        date.getDate() === tomorrow.getDate() &&
        date.getMonth() === tomorrow.getMonth() &&
        date.getFullYear() === tomorrow.getFullYear()
      ) {
        generatedNotifications.push({
          id: `${caseItem.id}-defer-${index}`,
          type: 'تأجيل',
          caseNumber: caseItem.fullCaseNumber || 'غير محدد',
          clientName: caseItem.clientName || 'غير محدد',
          courtLocation: caseItem.courtLocation || 'غير محددة',
          date: deferral.date,
          displayDate: new Date(deferral.date).toLocaleDateString('ar-EG', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
          }),
          reasons: deferral.reasons?.join('، ') || 'لا يوجد سبب محدد',
        });
      }
    });

    const actions = caseItem.actions || [];
    actions.forEach((action, index) => {
      if (action.isDeleted || !action.deadline) return;
      const deadline = new Date(action.deadline);
      if (
        deadline.getDate() === tomorrow.getDate() &&
        deadline.getMonth() === tomorrow.getMonth() &&
        deadline.getFullYear() === tomorrow.getFullYear()
      ) {
        generatedNotifications.push({
          id: `${caseItem.id}-action-${index}`,
          type: 'إجراء',
          caseNumber: caseItem.fullCaseNumber || 'غير محدد',
          clientName: caseItem.clientName || 'غير محدد',
          courtLocation: caseItem.courtLocation || 'غير محددة',
          date: action.deadline,
          displayDate: new Date(action.deadline).toLocaleDateString('ar-EG', {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
          }),
          description: action.description || 'لا يوجد وصف',
          linkedDeferralId: action.linkedDeferralId || '',
        });
      }
    });
  });

  return generatedNotifications;
};

const TopBar = ({ casesList, currentUser }) => {
  const [isProfileMenuOpen, setProfileMenuOpen] = useState(false);
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());
  const navigate = useNavigate();

  // لم نعد بحاجة إلى وظيفة تبديل الحساب النشط هنا
  // تم نقلها إلى صفحة البروفايل

  const { data: notifications = [] } = useQuery({
    queryKey: ['notifications', currentUser?.uid],
    queryFn: () => fetchNotifications(currentUser?.uid),
    enabled: !!currentUser,
    refetchInterval: 30000,
  });

  const profileDropdownRef = useRef(null);
  const profileButtonRef = useRef(null);
  const topBarRef = useRef(null);

  useEffect(() => {
    const updatePosition = () => {
      if (isProfileMenuOpen && profileButtonRef.current && profileDropdownRef.current) {
        const buttonRect = profileButtonRef.current.getBoundingClientRect();
        const topBarRect = topBarRef.current.getBoundingClientRect();
        profileDropdownRef.current.style.top = `${topBarRect.bottom + window.scrollY + 2}px`;
        profileDropdownRef.current.style.right = document.dir === 'rtl' ? '10px' : 'auto';
        profileDropdownRef.current.style.left = document.dir === 'rtl' ? 'auto' : '10px';
      }
    };

    const timer = setTimeout(updatePosition, 0);
    window.addEventListener('resize', updatePosition);
    window.addEventListener('scroll', updatePosition);
    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);
    };
  }, [isProfileMenuOpen]);

  const handleLogout = async () => {
    try {
      await signOut(auth);
      setProfileMenuOpen(false);
      navigate('/login');
    } catch (error) {
      console.error("خطأ أثناء تسجيل الخروج:", error);
    }
  };

  const handleAddCase = () => {
    navigate('/cases');
  };

  const handleNotificationsClick = () => {
    navigate('/notifications');
  };

  const toggleProfileMenu = () => {
    setProfileMenuOpen(!isProfileMenuOpen);
  };

  return (
    <>
      <header ref={topBarRef} className={styles.topBar}>
        <div className={styles.leftSection}>
          <div className={styles.iconWrapper}>
            <button
              className={styles.groupsButton}
              onClick={() => navigate('/groups')}
              aria-label="إدارة المجموعات"
            >
              <FiUsers size={20} />
              <span className={styles.tooltip}>المجموعات</span>
            </button>
          </div>
        </div>
        <div className={styles.rightSection}>
          <div className={styles.iconWrapper}>
            <button
              className={styles.iconButton}
              onClick={handleAddCase}
              aria-label="إضافة قضية جديدة"
            >
              <FiPlusCircle size={20} />
              <span className={styles.tooltip}>إضافة قضية</span>
            </button>
          </div>
          <div className={styles.iconWrapper}>
            <button
              className={styles.iconButton}
              aria-label="الإشعارات"
              onClick={handleNotificationsClick}
            >
              <FiBell size={20} />
              {notifications.length > 0 && (
                <span className={styles.notificationBadge}>{notifications.length}</span>
              )}
              <span className={styles.tooltip}>الإشعارات</span>
            </button>
          </div>
          <div className={styles.iconWrapper}>
            <button
              className={styles.iconButton}
              onClick={() => navigate('/dashboard')}
              aria-label="الصفحة الرئيسية"
            >
              <FiHome size={20} />
              <span className={styles.tooltip}>الرئيسية</span>
            </button>
          </div>
          <div className={styles.profileWrapper}>
            <button
              ref={profileButtonRef}
              className={styles.profileButton}
              onClick={toggleProfileMenu}
              aria-label="ملف المستخدم"
              aria-expanded={isProfileMenuOpen}
            >
              <div className={activeAccount === 'online' ? styles.userAvatarOnline : styles.userAvatarLocal}>
                {activeAccount === 'online' ? (
                  <FaGlobe className={styles.avatarIconOnline} />
                ) : (
                  <FaMobileAlt className={styles.avatarIconLocal} />
                )}
              </div>
              <div className={styles.userInfo}>
                <span className={styles.userName}>
                  {currentUser?.displayName || currentUser?.email || 'المستخدم'}
                </span>
              </div>
            </button>
          </div>
        </div>
      </header>
      {isProfileMenuOpen && (
        <div ref={profileDropdownRef} className={styles.profileDropdown}>
          <div
            className={styles.dropdownItem}
            onClick={() => {
              navigate('/profile');
              setProfileMenuOpen(false);
            }}
          >
            <span>الملف الشخصي</span>
            <FiUser size={14} />
          </div>
          <div
            className={styles.dropdownItem}
            onClick={() => {
              navigate('/settings');
              setProfileMenuOpen(false);
            }}
          >
            <span>إعدادات قوالب التأجيلات</span>
            <FiSettings size={14} />
          </div>
          <div
            className={styles.dropdownItem}
            onClick={handleLogout}
          >
            <span>تسجيل الخروج</span>
            <FiLogOut size={14} />
          </div>
        </div>
      )}
    </>
  );
};

export default TopBar;