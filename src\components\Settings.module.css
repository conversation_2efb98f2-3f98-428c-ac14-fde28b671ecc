.settingsContainer {
  min-height: 100vh;
  background-color: #f3f4f6;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  overflow-y: auto;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  max-width: 1000px;
  margin: 80px auto 20px auto;
  width: 100%;
  gap: 20px;
}

.title {
  color: #1f2937;
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 25px 35px;
  background: white;
  border-radius: 12px;
  border: 3px solid #74c0fc;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.title::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #74c0fc, #4dabf7, #339af0);
  border-radius: 12px 12px 0 0;
}

.icon {
  font-size: 1.8rem;
  color: #74c0fc;
  background: #f0f9ff;
  padding: 8px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tabs {
  display: flex;
  gap: 12px;
  background: white;
  border-radius: 12px;
  padding: 15px;
  border: 2px solid #e5e7eb;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.tabButton {
  background: #f9fafb;
  border: 2px solid #d1d5db;
  padding: 15px 25px;
  color: #4b5563;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  border-radius: 10px;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.tabButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(116, 192, 252, 0.1), transparent);
  transition: left 0.5s;
}

.tabButton:hover::before {
  left: 100%;
}

.tabButton:hover {
  background: #f0f9ff;
  color: #1f2937;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: #74c0fc;
}

.activeTab {
  background: linear-gradient(135deg, #74c0fc, #4dabf7);
  color: white;
  border-color: #339af0;
  box-shadow: 0 6px 25px rgba(116, 192, 252, 0.4);
  transform: translateY(-2px);
}

.activeTab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #339af0, #228be6);
  border-radius: 0 0 10px 10px;
}

.tabIcon {
  font-size: 1.2rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tabContent {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid #e5e7eb;
  flex: 1;
}

.section {
  color: #1f2937;
}

.section h2 {
  color: #1f2937;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-bottom: 3px solid #74c0fc;
  padding-bottom: 20px;
}

.section p {
  font-size: 1.2rem;
  line-height: 1.7;
  color: #4b5563;
  background: #f9fafb;
  padding: 25px;
  border-radius: 10px;
  border: 2px solid #e5e7eb;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* التجاوب للهواتف */
@media (max-width: 768px) {
  .settingsContainer {
    padding: 10px;
  }

  .content {
    margin: 70px auto 30px auto;
    padding: 15px;
    gap: 15px;
  }

  .title {
    font-size: 1.6rem;
    padding: 20px 25px;
  }

  .icon {
    width: 35px;
    height: 35px;
    font-size: 1.4rem;
  }

  .tabs {
    flex-direction: column;
    gap: 8px;
    padding: 12px;
  }

  .tabButton {
    padding: 12px 20px;
    font-size: 1rem;
  }

  .tabContent {
    padding: 20px;
  }

  .section h2 {
    font-size: 1.5rem;
  }

  .section p {
    font-size: 1rem;
    padding: 20px;
  }
}