.deferralTemplates {
  background-color: #f3f4f6;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

.content {
  width: 100%;
  max-width: 900px;
  margin: 80px auto 40px auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.templateForm {
  background: white;
  border-radius: 12px;
  padding: 30px;
  width: 100%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 3px solid #74c0fc;
  position: relative;
  overflow: hidden;
}

.templateForm::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #74c0fc, #4dabf7, #339af0);
  border-radius: 12px 12px 0 0;
}

.formField {
  margin-bottom: 18px;
}

.formField label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2937;
  font-size: 1rem;
}

/* تسميات الحقول الإجبارية والاختيارية */
.requiredLabel {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2937;
  font-size: 1rem;
}

.optionalLabel {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2937;
  font-size: 1rem;
}

.required {
  color: #ef4444;
  font-weight: 700;
  margin-left: 4px;
}

.optional {
  color: #6b7280;
  font-weight: 500;
  font-size: 0.9rem;
  margin-right: 8px;
}

.input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1f2937;
  outline: none;
  transition: all 0.3s ease;
}

.input:focus {
  border-color: #74c0fc;
  box-shadow: 0 0 0 3px rgba(116, 192, 252, 0.1);
}

.input::placeholder {
  color: #9ca3af;
}

.select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1f2937;
  outline: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.select:focus {
  border-color: #74c0fc;
  box-shadow: 0 0 0 3px rgba(116, 192, 252, 0.1);
}

.textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #1f2937;
  outline: none;
  transition: all 0.3s ease;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.textarea:focus {
  border-color: #74c0fc;
  box-shadow: 0 0 0 3px rgba(116, 192, 252, 0.1);
}

.textarea::placeholder {
  color: #9ca3af;
}

.actionRow {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 15px;
  background: #f9fafb;
  padding: 15px;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
}

.notificationField {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.removeButton {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: 2px solid #dc2626;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 40px;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.removeButton:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.addActionButton {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: 2px solid #059669;
  border-radius: 8px;
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
  transition: all 0.3s ease;
  font-weight: 600;
}

.addActionButton:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.saveButton {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: 2px solid #2563eb;
  border-radius: 8px;
  padding: 15px 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1.1rem;
}

.saveButton:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.templatesList {
  background: white;
  border-radius: 12px;
  padding: 25px;
  width: 100%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid #e5e7eb;
}

.templatesHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.templatesList h3 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  border-bottom: 3px solid #74c0fc;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.resetButton {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: 2px solid #d97706;
  border-radius: 8px;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
}

.resetButton:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.resetButton:disabled {
  background: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.templatesList p {
  color: #6b7280;
  font-size: 1rem;
  text-align: center;
  padding: 40px 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
}

.templateItem {
  background: #f9fafb;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 15px;
  color: #1f2937;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.templateItem:hover {
  border-color: #74c0fc;
  box-shadow: 0 2px 10px rgba(116, 192, 252, 0.1);
}

.templateItem strong {
  color: #374151;
  font-weight: 600;
}

.templateActions {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}

.editButton {
  background: #2563eb;
  color: white;
  border: 3px solid #2563eb;
  border-radius: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.editButton:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.deleteButton {
  background: #ef4444;
  color: white;
  border: 3px solid #ef4444;
  border-radius: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.deleteButton:hover {
  background: #dc2626;
  border-color: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

/* التجاوب للهواتف */
@media (max-width: 768px) {
  .deferralTemplates {
    padding: 15px;
  }

  .content {
    margin: 70px auto 30px auto;
    gap: 15px;
  }

  .templateForm, .templatesList {
    padding: 20px;
  }

  .templatesList h3 {
    font-size: 1.3rem;
  }

  .formField label {
    font-size: 0.9rem;
  }

  .input {
    font-size: 14px;
    padding: 10px 12px;
  }

  .actionRow {
    padding: 12px;
  }

  .addActionButton, .saveButton {
    font-size: 1rem;
    padding: 10px 16px;
  }

  .editButton, .deleteButton {
    font-size: 0.9rem;
    padding: 6px 12px;
  }

  .removeButton {
    width: 40px;
    height: 35px;
    padding: 6px;
  }

  .templateItem {
    padding: 15px;
  }

  .templateActions {
    gap: 8px;
    flex-wrap: wrap;
  }

  .templatesHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .resetButton {
    font-size: 0.8rem;
    padding: 8px 12px;
  }
}