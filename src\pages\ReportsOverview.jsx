import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaFileAlt, FaChevronLeft, FaCalendarAlt, FaSearch,
  FaUserTie, FaBalanceScale, FaChartPie, FaThLarge, FaFileSignature, FaLink,
  FaTable, FaGlobe, FaMobileAlt, FaSync
} from 'react-icons/fa';
import TopBar from '../components/topbar/TopBar';
import TimelinePage from './TimelinePage.jsx';
import styles from './ReportsOverview.module.css';
import { db } from '../config/firebaseConfig';
import { collection, query, where, getDocs, limit, startAfter, orderBy } from 'firebase/firestore';
import { getActiveAccount, getCases, setActiveAccount } from '../services/StorageService';

const ReportsOverview = ({ currentUser }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('cards');
  const [casesList, setCasesList] = useState([]);
  const [lastDoc, setLastDoc] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState(null);
  const [activeAccount, setActiveAccountState] = useState(getActiveAccount());

  const PAGE_SIZE = 10;
  const CACHE_KEY = 'cases_list';
  const CACHE_TTL = 5 * 60 * 1000;

  const fetchCases = async (isLoadMore = false, bypassCache = false) => {
    if (!currentUser) {
      setError('المستخدم غير متوفر. يرجى تسجيل الدخول.');
      return;
    }

    // التحقق من الحساب النشط
    const activeAccount = getActiveAccount();

    setLoading(true);
    setError(null);
    try {
      // استخدام التخزين المؤقت إذا كان متاحًا
      if (!isLoadMore && !bypassCache) {
        const cachedData = localStorage.getItem(CACHE_KEY);
        if (cachedData) {
          const { data, timestamp, account } = JSON.parse(cachedData);
          // استخدام التخزين المؤقت فقط إذا كان الحساب النشط هو نفسه المخزن
          if (Date.now() - timestamp < CACHE_TTL && account === activeAccount) {
            setCasesList(data);
            setLoading(false);
            return;
          }
        }
      }

      let newCases = [];

      // الحصول على القضايا حسب الحساب النشط
      if (activeAccount === 'online') {
        // إذا كان الحساب أونلاين وغير متصل بالإنترنت
        if (!navigator.onLine) {
          setError('غير متصل بالإنترنت. يرجى الاتصال بالإنترنت أو التبديل إلى الحساب المحلي.');
          setLoading(false);
          return;
        }

        // الحصول على القضايا من Firestore
        const casesRef = collection(db, 'cases');
        let q = query(
          casesRef,
          where('userId', '==', currentUser.uid),
          orderBy('updatedAt', 'desc'),
          limit(PAGE_SIZE)
        );

        if (isLoadMore && lastDoc) {
          q = query(
            casesRef,
            where('userId', '==', currentUser.uid),
            orderBy('updatedAt', 'desc'),
            startAfter(lastDoc),
            limit(PAGE_SIZE)
          );
        }

        const querySnapshot = await getDocs(q);
        newCases = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        setLastDoc(querySnapshot.docs[querySnapshot.docs.length - 1]);
        setHasMore(querySnapshot.docs.length === PAGE_SIZE);
      } else {
        // الحصول على القضايا من التخزين المحلي
        newCases = await getCases(currentUser.uid);

        // تنفيذ الترتيب والتقسيم يدويًا للقضايا المحلية
        newCases.sort((a, b) => {
          const dateA = new Date(a.updatedAt || a.createdAt || 0);
          const dateB = new Date(b.updatedAt || b.createdAt || 0);
          return dateB - dateA; // ترتيب تنازلي
        });

        if (isLoadMore) {
          // تحميل المزيد من القضايا المحلية (تقسيم الصفحات)
          const startIndex = casesList.length;
          newCases = newCases.slice(startIndex, startIndex + PAGE_SIZE);
        } else {
          // تحميل أول صفحة من القضايا المحلية
          newCases = newCases.slice(0, PAGE_SIZE);
        }

        setHasMore(newCases.length === PAGE_SIZE);
      }

      if (isLoadMore) {
        setCasesList(prev => [...prev, ...newCases]);
      } else {
        setCasesList(newCases);

        // تحديث التخزين المؤقت مع الحساب النشط
        localStorage.setItem(CACHE_KEY, JSON.stringify({
          data: newCases,
          timestamp: Date.now(),
          account: activeAccount, // تخزين الحساب النشط مع البيانات
        }));
      }
    } catch (e) {
      console.error('خطأ في جلب القضايا:', e);

      // رسالة خطأ مختلفة حسب الحساب النشط
      if (activeAccount === 'online') {
        setError('خطأ في جلب القضايا من السيرفر: ' + e.message);
      } else {
        setError('خطأ في جلب القضايا المحلية: ' + e.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const refreshCases = () => {
    fetchCases(false, true);
  };

  // لم نعد بحاجة إلى وظيفة تبديل الحساب النشط هنا
  // تم نقلها إلى صفحة البروفايل

  useEffect(() => {
    fetchCases(false, true);
    const interval = setInterval(() => fetchCases(), CACHE_TTL);
    return () => clearInterval(interval);
  }, [currentUser]);

  const getLastReportInfo = (deferrals) => {
    if (!deferrals || deferrals.length === 0) return { date: null, report: null };
    const activeDeferrals = deferrals.filter(deferral => !deferral.isDeleted);
    const lastDeferral = activeDeferrals[activeDeferrals.length - 1];
    if (!lastDeferral) return { date: null, report: null };
    return {
      date: lastDeferral.date ? new Date(lastDeferral.date) : null,
      report: lastDeferral.content || `${lastDeferral.date} - ${lastDeferral.reasons?.join('، ')}` || 'لا توجد تفاصيل'
    };
  };

  const getAllActionsInfo = (actions, deferrals) => {
    if (!actions || actions.length === 0) return [];

    // فلترة التنبيهات النشطة فقط (ليس الإجراءات المكتملة)
    const activeActions = actions.filter(action => !action.isDeleted && !action.isCompleted);

    return activeActions.map(action => {
      let linkedReport = null;
      if (action.linkedDeferralId && deferrals) {
        const activeDeferrals = deferrals.filter(deferral => !deferral.isDeleted);
        const deferralIndex = activeDeferrals.findIndex((deferral, index) =>
          `${action.id}-defer-${index}` === action.linkedDeferralId
        );
        if (deferralIndex !== -1) {
          linkedReport = activeDeferrals[deferralIndex].content ||
                        `${activeDeferrals[deferralIndex].date} - ${activeDeferrals[deferralIndex].reasons?.join('، ')}`;
        }
      }

      return {
        date: action.deadline ? new Date(action.deadline) : null,
        description: action.description || 'لا توجد تفاصيل',
        linkedReport: linkedReport || null,
        type: 'action'
      };
    }).sort((a, b) => (b.date || 0) - (a.date || 0)); // ترتيب تنازلي حسب التاريخ
  };

  const getAllDeferralsInfo = (deferrals) => {
    if (!deferrals || deferrals.length === 0) return [];

    const activeDeferrals = deferrals.filter(deferral => !deferral.isDeleted);

    return activeDeferrals.map(deferral => ({
      date: deferral.date ? new Date(deferral.date) : null,
      description: deferral.content || deferral.reasons?.join('، ') || 'لا توجد تفاصيل',
      type: 'deferral'
    })).sort((a, b) => (b.date || 0) - (a.date || 0)); // ترتيب تنازلي حسب التاريخ
  };

  const searchTermLower = searchTerm.toLowerCase();
  const casesToDisplay = casesList.reduce((acc, caseItem) => {
    try {
      const lastReportInfo = getLastReportInfo(caseItem.deferrals);
      const allActions = getAllActionsInfo(caseItem.actions, caseItem.deferrals);
      const allDeferrals = getAllDeferralsInfo(caseItem.deferrals);

      const enhancedCase = {
        ...caseItem,
        lastReport: lastReportInfo.report,
        lastReportDate: lastReportInfo.date,
        allActions: allActions,
        allDeferrals: allDeferrals,
        // الحفاظ على آخر تنبيه للتوافق مع الكود الموجود
        lastAction: allActions.length > 0 ? allActions[0].description : null,
        lastActionDate: allActions.length > 0 ? allActions[0].date : null,
        linkedReport: allActions.length > 0 ? allActions[0].linkedReport : null,
      };

      const matchesSearch =
        (enhancedCase.fullCaseNumber || '').toLowerCase().includes(searchTermLower) ||
        (enhancedCase.clientName || '').toLowerCase().includes(searchTermLower) ||
        (enhancedCase.courtLocation || '').toLowerCase().includes(searchTermLower) ||
        (enhancedCase.lastReport || '').toLowerCase().includes(searchTermLower) ||
        (enhancedCase.lastAction || '').toLowerCase().includes(searchTermLower);

      return matchesSearch ? [...acc, enhancedCase] : acc;
    } catch (e) {
      console.error(`خطأ في معالجة القضية ${caseItem.id}:`, e);
      return acc;
    }
  }, []);

  const handleBack = () => navigate('/dashboard');
  const handleCaseClick = (caseItem) => navigate(`/case-details/${caseItem.id}`);

  const formatDate = (date) => {
    return date ? new Date(date).toLocaleDateString('ar-EG', {
      month: 'numeric',
      day: 'numeric',
    }) : '—';
  };

  const getCaseTitle = (caseItem) => {
    switch (caseItem.caseStatus || caseItem.case_status) {
      case 'دعوى قضائية':
        return `قضية رقم: ${caseItem.fullCaseNumber || ''}`;
      case 'محضر':
        return `محضر رقم: ${caseItem.fullCaseNumber || ''}`;
      case 'قيد النظر':
        return 'ملف قيد النظر';
      default:
        return `قضية رقم: ${caseItem.fullCaseNumber || ''}`;
    }
  };

  const getCardBorderClass = (caseStatus) => {
    switch (caseStatus) {
      case 'دعوى قضائية':
        return styles.cardBorderLawsuit;
      case 'محضر':
        return styles.cardBorderReport;
      case 'قيد النظر':
        return styles.cardBorderPending;
      default:
        return styles.cardBorderDefault;
    }
  };

  const renderCases = () => {
    if (error) {
      return <div className={styles.noCases} style={{ color: 'red' }}>{error}</div>;
    }

    if (loading) {
      return <div className={styles.noCases}>جاري التحميل...</div>;
    }

    if (casesToDisplay.length === 0) {
      return (
        <div className={styles.noCases}>
          {searchTerm ? 'لا توجد نتائج مطابقة للبحث' : 'لا توجد قضايا مسجلة'}
        </div>
      );
    }

    if (viewMode === 'table') {
      return (
        <div className={styles.casesTableContainer}>
          <table className={styles.casesTable}>
            <thead>
              <tr>
                <th>اسم الموكل</th>
                <th>مكان المحكمة</th>
                <th>آخر ما تم</th>
              </tr>
            </thead>
            <tbody>
              {casesToDisplay.map((caseItem) => (
                <tr
                  key={caseItem.id}
                  onClick={() => handleCaseClick(caseItem)}
                  className={
                    (caseItem.caseStatus || caseItem.case_status) === 'قيد النظر'
                      ? styles.rowPending
                      : (caseItem.caseStatus || caseItem.case_status) === 'دعوى قضائية'
                      ? styles.rowLawsuit
                      : styles.rowDefault
                  }
                >
                  <td>{caseItem.clientName || 'غير محدد'}</td>
                  <td>{caseItem.courtLocation || 'غير محدد'}</td>
                  <td>
                    {caseItem.lastAction ? (
                      <>
                        {caseItem.lastAction.substring(0, 20) + (caseItem.lastAction.length > 20 ? '...' : '')}
                        <br />
                        <span className={styles.dateText}>
                          {formatDate(caseItem.lastActionDate)}
                        </span>
                      </>
                    ) : caseItem.lastReport ? (
                      <>
                        {caseItem.lastReport.substring(0, 20) + (caseItem.lastReport.length > 20 ? '...' : '')}
                        <br />
                        <span className={styles.dateText}>
                          {formatDate(caseItem.lastReportDate)}
                        </span>
                      </>
                    ) : (
                      '—'
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {hasMore && (
            <button onClick={() => fetchCases(true)} className={styles.loadMoreButton} disabled={loading}>
              {loading ? 'جاري التحميل...' : 'تحميل المزيد'}
            </button>
          )}
        </div>
      );
    }

    return viewMode === 'cards' ? (
      <div className={styles.casesGrid}>
        {casesToDisplay.map((caseItem) => (
          <div
            key={caseItem.id}
            className={`${styles.caseCard} ${getCardBorderClass(caseItem.caseStatus || caseItem.case_status)}`}
            onClick={() => handleCaseClick(caseItem)}
          >
            <div className={styles.cardHeader}>
              <h3 className={styles.caseNumber}>
                <FaFileAlt className={styles.caseIcon} />
                {getCaseTitle(caseItem)}
              </h3>
              <span className={styles.reportDate}>
                <FaCalendarAlt /> {formatDate(caseItem.lastActionDate || caseItem.lastReportDate)}
              </span>
            </div>

            <div className={styles.cardBody}>
              <div className={styles.infoGroup}>
                <FaUserTie className={styles.infoIcon} />
                <div>
                  <span className={styles.infoLabel}>الموكل:</span>
                  <span className={styles.infoValue}>{caseItem.clientName || 'غير محدد'}</span>
                </div>
              </div>

              <div className={styles.infoGroup}>
                <FaBalanceScale className={styles.infoIcon} />
                <div>
                  <span className={styles.infoLabel}>
                    {(caseItem.caseStatus || caseItem.case_status) === 'دعوى قضائية' ? 'المحكمة:' : 'مكان الجهة المختصة:'}
                  </span>
                  <span className={styles.infoValue}>{caseItem.courtLocation || 'غير محددة'}</span>
                </div>
              </div>

              {(caseItem.allActions.length > 0 || caseItem.allDeferrals.length > 0) && (
                <div className={styles.previewContainer}>
                  {/* عرض التنبيهات النشطة */}
                  {caseItem.allActions.length > 0 && (
                    <div className={styles.actionsSection}>
                      <h4 className={styles.sectionTitle}>التنبيهات النشطة ({caseItem.allActions.length}):</h4>
                      <div className={styles.itemsList}>
                        {caseItem.allActions.slice(0, 3).map((action, index) => (
                          <div key={index} className={styles.actionItem}>
                            <FaFileSignature className={styles.actionIcon} />
                            <div className={styles.itemContent}>
                              <span className={styles.itemText}>
                                {action.description.length > 50 ?
                                  action.description.substring(0, 50) + '...' :
                                  action.description}
                              </span>
                              <span className={styles.itemDate}>{formatDate(action.date)}</span>
                              {action.linkedReport && (
                                <span className={styles.linkedInfo}>
                                  <FaLink className={styles.linkIcon} />
                                  مرتبط بتقرير
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                        {caseItem.allActions.length > 3 && (
                          <div className={styles.moreItems}>
                            +{caseItem.allActions.length - 3} تنبيهات أخرى
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* عرض تنبيهات الجلسات */}
                  {caseItem.allDeferrals.length > 0 && (
                    <div className={styles.deferralsSection}>
                      <h4 className={styles.sectionTitle}>تنبيهات الجلسات ({caseItem.allDeferrals.length}):</h4>
                      <div className={styles.itemsList}>
                        {caseItem.allDeferrals.slice(0, 2).map((deferral, index) => (
                          <div key={index} className={styles.deferralItem}>
                            <FaCalendarAlt className={styles.deferralIcon} />
                            <div className={styles.itemContent}>
                              <span className={styles.itemText}>
                                {deferral.description.length > 50 ?
                                  deferral.description.substring(0, 50) + '...' :
                                  deferral.description}
                              </span>
                              <span className={styles.itemDate}>{formatDate(deferral.date)}</span>
                            </div>
                          </div>
                        ))}
                        {caseItem.allDeferrals.length > 2 && (
                          <div className={styles.moreItems}>
                            +{caseItem.allDeferrals.length - 2} تنبيهات أخرى
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
        {hasMore && (
          <button onClick={() => fetchCases(true)} className={styles.loadMoreButton} disabled={loading}>
            {loading ? 'جاري التحميل...' : 'تحميل المزيد'}
          </button>
        )}
      </div>
    ) : (
      <div className={styles.casesContainer}>
        <TimelinePage casesList={casesToDisplay} getCaseTitle={getCaseTitle} />
      </div>
    );
  };

  return (
    <div className={styles.pageContainer}>
      <TopBar title="نظام إدارة التقارير القانونية" showBackButton onBack={handleBack} />
      <div className={styles.contentContainer}>
        <div className={styles.header}>
          <h1 className={styles.pageTitle}>نظرة عامة على القضايا</h1>
        </div>

        <div className={styles.controlsContainer}>
          <div className={styles.searchBox}>
            <FaSearch className={styles.searchIcon} />
            <input
              type="text"
              placeholder="ابحث عن قضية أو موكل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.viewToggle}>
            <button
              className={`${styles.viewButton} ${viewMode === 'cards' ? styles.active : ''}`}
              onClick={() => setViewMode('cards')}
            >
              <FaThLarge /> عرض البطاقات
            </button>
            <button
              className={`${styles.viewButton} ${viewMode === 'radar' ? styles.active : ''}`}
              onClick={() => setViewMode('radar')}
            >
              <FaChartPie /> عرض الرادار
            </button>
            <button
              className={`${styles.viewButton} ${viewMode === 'table' ? styles.active : ''}`}
              onClick={() => setViewMode('table')}
            >
              <FaTable /> عرض الجدول
            </button>
          </div>
        </div>

        <div className={styles.casesContainer}>{renderCases()}</div>
      </div>
    </div>
  );
};

export default ReportsOverview;