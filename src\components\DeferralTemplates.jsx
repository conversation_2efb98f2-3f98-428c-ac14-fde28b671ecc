import React, { useState, useEffect } from 'react';
import { FaPlus, FaTrashAlt, FaSave } from 'react-icons/fa';
import TopBar from './topbar/TopBar';
import { db } from '../config/firebaseConfig';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, query, where, getDoc } from 'firebase/firestore';
import styles from './DeferralTemplates.module.css';

const LINKAGE_TYPES = {
  SEQUENTIAL: 'تسلسلي',
  DIRECT: 'مباشر',
};

const DEFERRAL_CATEGORIES = {
  JUDGMENT: 'للحكم',
  ANNOUNCEMENT: 'للإعلان',
  MEMORANDUMS: 'للمذكرات',
  REPORT: 'للتقرير',
  DOCUMENTS: 'للمستندات',
  REPORT_RECEIVED: 'لورود التقرير',
  OTHER: 'أخرى',
};

// القوالب الافتراضية
const DEFAULT_TEMPLATES = [
  {
    category: DEFERRAL_CATEGORIES.JUDGMENT,
    actions: [
      {
        description: 'مراجعة الأوراق والمستندات',
        linkage: { type: LINKAGE_TYPES.SEQUENTIAL }
      },
      {
        description: 'إعداد المرافعة النهائية',
        linkage: { type: LINKAGE_TYPES.SEQUENTIAL }
      }
    ]
  },
  {
    category: DEFERRAL_CATEGORIES.ANNOUNCEMENT,
    actions: [
      {
        description: 'متابعة إجراءات الإعلان',
        linkage: { type: LINKAGE_TYPES.DIRECT }
      }
    ]
  },
  {
    category: DEFERRAL_CATEGORIES.MEMORANDUMS,
    actions: [
      {
        description: 'إعداد المذكرة القانونية',
        linkage: { type: LINKAGE_TYPES.SEQUENTIAL }
      },
      {
        description: 'مراجعة وتدقيق المذكرة',
        linkage: { type: LINKAGE_TYPES.SEQUENTIAL }
      }
    ]
  },
  {
    category: DEFERRAL_CATEGORIES.REPORT,
    actions: [
      {
        description: 'متابعة إعداد التقرير',
        linkage: { type: LINKAGE_TYPES.DIRECT }
      }
    ]
  },
  {
    category: DEFERRAL_CATEGORIES.DOCUMENTS,
    actions: [
      {
        description: 'جمع المستندات المطلوبة',
        linkage: { type: LINKAGE_TYPES.SEQUENTIAL }
      },
      {
        description: 'مراجعة وتنظيم المستندات',
        linkage: { type: LINKAGE_TYPES.SEQUENTIAL }
      }
    ]
  },
  {
    category: DEFERRAL_CATEGORIES.REPORT_RECEIVED,
    actions: [
      {
        description: 'مراجعة التقرير الوارد',
        linkage: { type: LINKAGE_TYPES.SEQUENTIAL }
      },
      {
        description: 'إعداد الرد على التقرير',
        linkage: { type: LINKAGE_TYPES.SEQUENTIAL }
      }
    ]
  },
  {
    category: DEFERRAL_CATEGORIES.OTHER,
    actions: [
      {
        description: 'متابعة الإجراءات المطلوبة',
        linkage: { type: LINKAGE_TYPES.DIRECT }
      }
    ]
  }
];

const DeferralTemplates = ({ currentUser }) => {
  const [templates, setTemplates] = useState([]);
  const [newTemplate, setNewTemplate] = useState({
    category: '', // تصنيف التأجيل الجديد - إجباري
    actions: [{
      description: '',
      linkage: {
        type: LINKAGE_TYPES.SEQUENTIAL,
      }
    }],
  });
  const [editingTemplateId, setEditingTemplateId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // دالة لإنشاء القوالب الافتراضية
  const createDefaultTemplates = async () => {
    try {
      for (const template of DEFAULT_TEMPLATES) {
        await addDoc(collection(db, 'deferralTemplates'), {
          userId: currentUser.uid,
          category: template.category,
          actions: template.actions,
          isDefault: true,
          createdAt: new Date().toISOString(),
        });
      }
    } catch (e) {
      console.error('خطأ في إنشاء القوالب الافتراضية:', e);
    }
  };

  const fetchTemplates = async () => {
    if (!currentUser || !currentUser.uid) {
      setLoading(false);
      setError('لا يوجد مستخدم مسجل الدخول.');
      return;
    }

    try {
      setLoading(true);
      const templatesRef = collection(db, 'deferralTemplates');
      const q = query(templatesRef, where('userId', '==', currentUser.uid));
      const querySnapshot = await getDocs(q);
      const userTemplates = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      // إذا لم توجد قوالب، أنشئ القوالب الافتراضية
      if (userTemplates.length === 0) {
        await createDefaultTemplates();
        // إعادة جلب القوالب بعد الإنشاء
        const newQuerySnapshot = await getDocs(q);
        const newUserTemplates = newQuerySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        setTemplates(newUserTemplates);
      } else {
        setTemplates(userTemplates);
      }

      setError(null);
    } catch (e) {
      setError('خطأ في جلب القوالب: ' + e.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, [currentUser]);

  const addActionField = () => {
    if (newTemplate.actions.length >= 3) return; // الحد الأقصى 3 إجراءات
    setNewTemplate({
      ...newTemplate,
      actions: [...newTemplate.actions, {
        description: '',
        linkage: {
          type: LINKAGE_TYPES.SEQUENTIAL,
        }
      }],
    });
  };

  const updateActionField = (index, field, value) => {
    const updatedActions = [...newTemplate.actions];
    updatedActions[index] = { ...updatedActions[index], [field]: value };
    setNewTemplate({ ...newTemplate, actions: updatedActions });
  };

  const updateLinkageField = (index, field, value) => {
    const updatedActions = [...newTemplate.actions];
    updatedActions[index].linkage = { ...updatedActions[index].linkage, [field]: value };
    setNewTemplate({ ...newTemplate, actions: updatedActions });
  };

  const removeActionField = (index) => {
    setNewTemplate({
      ...newTemplate,
      actions: newTemplate.actions.filter((_, i) => i !== index),
    });
  };

  const handleSaveTemplate = async () => {
    // التحقق من الحقول الإجبارية: تصنيف التأجيل
    if (!newTemplate.category) {
      alert('يرجى اختيار تصنيف التأجيل');
      return;
    }

    // التحقق من الإجراءات المرتبطة (اختياري - فقط إذا تم إدخال إجراءات)
    const hasActions = newTemplate.actions.some(action => action.description.trim());
    if (hasActions && newTemplate.actions.some(action => action.description.trim() && !action.description)) {
      alert('يرجى ملء وصف جميع الإجراءات المدخلة أو حذف الإجراءات الفارغة');
      return;
    }

    try {
      setLoading(true);
      if (editingTemplateId) {
        const templateRef = doc(db, 'deferralTemplates', editingTemplateId);
        await updateDoc(templateRef, {
          category: newTemplate.category,
          actions: newTemplate.actions.filter(action => action.description.trim()), // حفظ الإجراءات غير الفارغة فقط
          updatedAt: new Date().toISOString(),
        });
        alert('تم تعديل القالب بنجاح');
      } else {
        await addDoc(collection(db, 'deferralTemplates'), {
          userId: currentUser.uid,
          category: newTemplate.category,
          actions: newTemplate.actions.filter(action => action.description.trim()), // حفظ الإجراءات غير الفارغة فقط
          createdAt: new Date().toISOString(),
        });
        alert('تم إضافة القالب بنجاح');
      }
      setNewTemplate({
        category: '',
        actions: [{
          description: '',
          linkage: {
            type: LINKAGE_TYPES.SEQUENTIAL,
          }
        }],
      });
      setEditingTemplateId(null);
      fetchTemplates();
    } catch (e) {
      alert('خطأ في حفظ القالب: ' + e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEditTemplate = (template) => {
    setNewTemplate({
      // دعم البيانات القديمة: إذا كان هناك reason بدلاً من category
      category: template.category || template.reason || '',
      actions: template.actions ? template.actions.map(action => ({
        ...action,
        linkage: {
          type: action.linkage?.type || action.notification?.type || LINKAGE_TYPES.SEQUENTIAL,
        },
      })) : [{
        description: '',
        linkage: {
          type: LINKAGE_TYPES.SEQUENTIAL,
        }
      }],
    });
    setEditingTemplateId(template.id);
  };

  const handleDeleteTemplate = async (templateId) => {
    try {
      setLoading(true);
      await deleteDoc(doc(db, 'deferralTemplates', templateId));
      fetchTemplates();
      alert('تم حذف القالب بنجاح');
    } catch (e) {
      alert('خطأ في حذف القالب: ' + e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleResetToDefaults = async () => {
    if (!confirm('هل أنت متأكد من إعادة تعيين جميع القوالب إلى القوالب الافتراضية؟ سيتم حذف جميع القوالب المخصصة.')) {
      return;
    }

    try {
      setLoading(true);

      // حذف جميع القوالب الحالية
      const templatesRef = collection(db, 'deferralTemplates');
      const q = query(templatesRef, where('userId', '==', currentUser.uid));
      const querySnapshot = await getDocs(q);

      for (const docSnapshot of querySnapshot.docs) {
        await deleteDoc(doc(db, 'deferralTemplates', docSnapshot.id));
      }

      // إنشاء القوالب الافتراضية
      await createDefaultTemplates();

      // إعادة جلب القوالب
      await fetchTemplates();

      alert('تم إعادة تعيين القوالب الافتراضية بنجاح');
    } catch (e) {
      alert('خطأ في إعادة تعيين القوالب: ' + e.message);
    } finally {
      setLoading(false);
    }
  };

  const applyTemplate = async (template, caseNumber, deferralDate) => {
    if (!caseNumber || !deferralDate) {
      setError('رقم القضية أو تاريخ التأجيل غير متوفر.');
      return;
    }

    try {
      const caseRef = doc(db, 'cases', caseNumber);
      const caseDoc = await getDoc(caseRef);
      if (!caseDoc.exists()) {
        setError('القضية غير موجودة.');
        return;
      }
      const caseData = caseDoc.data() || {};
      if (caseData.userId !== currentUser.uid) {
        setError('لا تمتلك صلاحية لتعديل هذه القضية.');
        return;
      }
      const deferralId = `${caseNumber}-defer-${Date.now()}`;
      const newDeferral = {
        id: deferralId,
        date: deferralDate,
        reasons: [template.category || template.reason], // دعم البيانات القديمة
        createdAt: new Date().toISOString(),
        isDeleted: false,
        isArchived: false,
      };

      const updatedDeferrals = [...(caseData.deferrals || []), newDeferral];
      await updateDoc(caseRef, { deferrals: updatedDeferrals });

      const newActions = template.actions.map((action, index) => {
        const actionDeadline = new Date(deferralDate).toISOString().split('T')[0];

        const actionId = `${caseNumber}-action-${Date.now()}-${index}`;
        let linkedActionId = '';

        const isSequential = template.actions[0].linkage.type === LINKAGE_TYPES.SEQUENTIAL;
        if (isSequential && index > 0) {
          linkedActionId = `${caseNumber}-action-${Date.now()}-${index - 1}`;
        }

        return {
          id: actionId,
          description: action.description,
          deadline: actionDeadline,
          linkedDeferralId: deferralId,
          linkedActionId: linkedActionId,
          createdAt: new Date().toISOString(),
          isDeleted: false,
          isArchived: false,
        };
      });

      const updatedActions = [...(caseData.actions || []), ...newActions];
      await updateDoc(caseRef, { actions: updatedActions });

      alert('تم تطبيق القالب وإضافة التأجيل والإجراءات بنجاح');
    } catch (e) {
      setError('خطأ في تطبيق القالب: ' + e.message);
    }
  };

  if (loading) {
    return <div>جاري تحميل القوالب...</div>;
  }

  if (error) {
    return <div style={{ color: 'red' }}>{error}</div>;
  }

  return (
    <div className={styles.deferralTemplates}>
      <div className={styles.content}>
        <div className={styles.templateForm}>
          <div className={styles.formField}>
            <label className={styles.requiredLabel}>
              تصنيف التأجيل <span className={styles.required}>*</span>
            </label>
            <select
              value={newTemplate.category}
              onChange={(e) => setNewTemplate({ ...newTemplate, category: e.target.value })}
              className={styles.select}
              required
            >
              <option value="">اختر تصنيف التأجيل</option>
              <option value={DEFERRAL_CATEGORIES.JUDGMENT}>{DEFERRAL_CATEGORIES.JUDGMENT}</option>
              <option value={DEFERRAL_CATEGORIES.ANNOUNCEMENT}>{DEFERRAL_CATEGORIES.ANNOUNCEMENT}</option>
              <option value={DEFERRAL_CATEGORIES.MEMORANDUMS}>{DEFERRAL_CATEGORIES.MEMORANDUMS}</option>
              <option value={DEFERRAL_CATEGORIES.REPORT}>{DEFERRAL_CATEGORIES.REPORT}</option>
              <option value={DEFERRAL_CATEGORIES.DOCUMENTS}>{DEFERRAL_CATEGORIES.DOCUMENTS}</option>
              <option value={DEFERRAL_CATEGORIES.REPORT_RECEIVED}>{DEFERRAL_CATEGORIES.REPORT_RECEIVED}</option>
              <option value={DEFERRAL_CATEGORIES.OTHER}>{DEFERRAL_CATEGORIES.OTHER}</option>
            </select>
          </div>

          <div className={styles.formField}>
            <label className={styles.optionalLabel}>
              الإجراءات المرتبطة
              <span className={styles.optional}>(اختياري)</span>
            </label>
            {newTemplate.actions.map((action, index) => (
              <div key={index} className={styles.actionRow}>
                <input
                  type="text"
                  value={action.description}
                  onChange={(e) => updateActionField(index, 'description', e.target.value)}
                  placeholder="وصف الإجراء"
                  className={styles.input}
                />
                {newTemplate.actions.length > 1 && index === 0 && (
                  <div className={styles.notificationField}>
                    <label>طريقة الارتباط: <span style={{ fontSize: '0.9em', color: '#666' }}>(تسلسلي: كل إجراء لازم يخلّص قبل اللي بعده - مباشر: كل إجراء مستقل لوحده)</span></label>
                    <select
                      value={action.linkage.type}
                      onChange={(e) => {
                        const updatedActions = newTemplate.actions.map((act, i) => ({
                          ...act,
                          linkage: { ...act.linkage, type: e.target.value }
                        }));
                        setNewTemplate({ ...newTemplate, actions: updatedActions });
                      }}
                      className={styles.input}
                    >
                      <option value={LINKAGE_TYPES.SEQUENTIAL}>تسلسلي</option>
                      <option value={LINKAGE_TYPES.DIRECT}>مباشر</option>
                    </select>
                  </div>
                )}
                {newTemplate.actions.length > 1 && (
                  <button
                    onClick={() => removeActionField(index)}
                    className={styles.removeButton}
                  >
                    <FaTrashAlt />
                  </button>
                )}
              </div>
            ))}
            {newTemplate.actions.length < 3 && (
              <button onClick={addActionField} className={styles.addActionButton}>
                <FaPlus /> إضافة إجراء
              </button>
            )}
          </div>

          <button onClick={handleSaveTemplate} className={styles.saveButton}>
            <FaSave /> {editingTemplateId ? 'حفظ التعديلات' : 'حفظ القالب'}
          </button>
        </div>

        <div className={styles.templatesList}>
          <div className={styles.templatesHeader}>
            <h3>القوالب المحفوظة:</h3>
            <button
              onClick={handleResetToDefaults}
              className={styles.resetButton}
              disabled={loading}
            >
              إعادة تعيين القوالب الافتراضية
            </button>
          </div>
          {templates.length === 0 ? (
            <p>لا توجد قوالب محفوظة بعد.</p>
          ) : (
            templates.map((template) => (
              <div key={template.id} className={styles.templateItem}>
                <div>
                  <strong>التصنيف:</strong> {template.category || template.reason}
                </div>
                <div>
                  <strong>الإجراءات:</strong>
                  <ul>
                    {template.actions.map((action, index) => (
                      <li key={index}>
                        {action.description} -
                        {index === 0 && `${template.actions[0].linkage.type}`}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className={styles.templateActions}>
                  <button onClick={() => handleEditTemplate(template)} className={styles.editButton}>
                    تعديل
                  </button>
                  <button onClick={() => handleDeleteTemplate(template.id)} className={styles.deleteButton}>
                    <FaTrashAlt />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default DeferralTemplates;