:root {
  /* ألوان أساسية */
  --primary-color: #2563eb;
  --primary-dark: #1e40af;
  --primary-light: #60a5fa;

  /* ألوان ثانوية */
  --secondary-color: #10b981;
  --secondary-dark: #059669;
  --secondary-light: #34d399;

  /* ألوان محايدة */
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;

  /* ألوان الحالة */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* متغيرات التصميم */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;

  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* التحولات */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  background-color: var(--neutral-100);
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Cairo', sans-serif;
  color: var(--neutral-800);
}

html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  direction: rtl;
  font-family: 'Tajawal', sans-serif;
}


.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  max-width: 1200px;
  margin: auto;
  padding: 20px;
  gap: 20px;
  box-sizing: border-box;
}



.cases-sidebar {
  flex: 1;
  max-width: 300px;
  background-color: #f5f5f5;
  padding: 5px;
  border-radius: 5px;
  height: 100%;
  overflow-y: auto;
}

button {
  padding: 12px 25px;
  margin-top: 5px;
  font-size: 16px;
  border-radius: var(--radius-md);
  cursor: pointer;
  background-color: var(--primary-color);
  color: white;
  border: none;
  transition: all var(--transition-normal);
  font-family: 'Tajawal', sans-serif !important;
  font-weight: 600 !important;
  width: 100%;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

button:hover {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

button:active {
  transform: translateY(0);
}

.submit-btn {
  background-color: var(--secondary-color);
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 16px;
  width: 100%;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.submit-btn:hover {
  background-color: var(--secondary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.submit-btn:active {
  transform: translateY(0);
}

h2 {
  margin-bottom: 5px;
  font-size: 24px;
  color: #333;
}

p {
  margin-bottom: 5px;
  font-size: 16px;
  color: #555;
}

.case-registration-container label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--neutral-700);
  margin-top: 16px;
}

.case-registration-container input[type="text"],
.case-registration-container input[type="date"],
.case-registration-container input[type="email"],
.case-registration-container input[type="password"],
.case-registration-container input[type="number"],
.case-registration-container input[type="tel"],
.case-registration-container textarea {
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: var(--radius-md);
  font-size: 16px;
  box-sizing: border-box;
  background-color: white;
  border: 1px solid var(--neutral-300);
  color: var(--neutral-800);
  transition: all var(--transition-fast);
}

.case-registration-container input:focus,
.case-registration-container textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group {
  margin-bottom: 16px;
}

.input-group select,
select {
  width: 100%;
  padding: 12px 16px;
  border-radius: var(--radius-md);
  font-family: 'Tajawal', sans-serif;
  background-color: white;
  border: 1px solid var(--neutral-300);
  color: var(--neutral-800);
  font-size: 16px;
  transition: all var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 16px center;
  padding-left: 40px;
}

.input-group select:focus,
select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.case-number-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.case-number-inputs input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.separator {
  font-weight: bold;
}

.generated-number {
  margin-top: 5px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

label {
  display: block;
  font-weight: bold;
  margin-top: 5px;
}
/* تم تعريف أنماط الأزرار بالفعل في الأعلى */
/* تنسيقات Progress Bar */
.progress-bar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: #e0e0e0;
  z-index: 1000;
}

.progress-bar {
  height: 100%;
  background: #4CAF50;
  width: 0%;
  transition: width 0.4s ease;
  border-radius: 0 2px 2px 0;
}

/* تعديلات للهيكل العام */
.container {
  position: relative;
  padding-top: 10px; /* إضافة مساحة للـ Progress Bar */
}

