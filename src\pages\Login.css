.login-container {
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-image: url('/soft6.jpg');
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-box {
  background-color: rgba(255, 255, 255, 0.15);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  width: 420px;
  max-width: 90%;
  text-align: center;
  transition: all 0.3s ease;
}

.login-box:hover {
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
  transform: translateY(-5px);
}

.login-container h2 {
  color: #fff;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.form-group {
  margin-bottom: 20px;
  text-align: right;
}

.form-group label {
  display: block;
  color: #fff;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
}

.form-group input[type='email'],
.form-group input[type='password'] {
  width: 100%;
  padding: 12px 15px;
  border: none;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.85);
  color: #333;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(15, 45, 82, 0.3);
  background-color: rgba(255, 255, 255, 0.95);
}

button[type='submit'] {
  background-color: #0f2d52;
  color: #fff;
  padding: 14px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

button[type='submit']:hover {
  background-color: #1a4b8c;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

button[type='submit']:active {
  transform: translateY(0);
}

button[type='submit']:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  color: #ff6b6b;
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(255, 0, 0, 0.1);
  border-radius: 5px;
  border-left: 4px solid #ff6b6b;
}

.success-message {
  color: #51cf66;
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(81, 207, 102, 0.1);
  border-radius: 5px;
  border-left: 4px solid #51cf66;
}

.login-container p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 15px;
  margin-top: 20px;
  text-align: center;
}

.login-container p a {
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.3);
  padding-bottom: 2px;
}

.login-container p a:hover {
  text-decoration: none;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.8);
  color: #ffffff;
}

.loader {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #ffffff;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  margin-bottom: 20px;
}

/* تأثيرات للهواتف */
@media (max-width: 480px) {
  .login-box {
    padding: 30px 20px;
    width: 95%;
  }
  
  .login-container h2 {
    font-size: 24px;
  }
  
  .form-group input[type='email'],
  .form-group input[type='password'] {
    padding: 10px 12px;
  }
}