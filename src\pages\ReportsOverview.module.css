/* الأساسيات */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #10b981;
  --danger-color: #ef4444;
  --dark-color: #334155;
  --light-color: #f8fafc;
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --transition: all 0.2s ease;

  /* لوحة الألوان الحمراء الإضافية */
  --red-primary: #622872; /* بنفسجي داكن */
  --red-secondary: #caa5cb; /* وردي فاتح */
  --red-light: #e8ddea; /* وردي باهت */
  --red-lightest: #faeaf6; /* وردي فاتح جداً */

  /* لوحة الألوان الزرقاء الإضافية */
  --blue-darkest: #00033a; /* أزرق داكن جداً */
  --blue-dark-alt: #162647; /* أزرق داكن بديل */
  --blue-medium-alt: #163473; /* أزرق متوسط بديل */
  --accent-gold: #d2ab17; /* ذهبي */
}

/* الهيكل العام */
.pageContainer {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: 'Inter', 'Cairo', sans-serif;
  background-color: #f1f5f9;
}

/* الحاوية الأساسية بتأثير الزجاج لعرض التقارير */
.container {
  border-radius: 16px;
  padding: 20px;
  margin: 20px auto;
  width: 95%;
  max-width: 1200px;
  color: #fff;
}

/* المحتوى الرئيسي */
.contentContainer {
  padding: 12px;
  margin-top: -5px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

/* العنوان */
.header {
  margin-bottom: 15px;
  text-align: center;
}

.pageTitle {
  font-size: 20px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 10px;
  text-align: center;
  background-color: white;
  padding: 8px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* فلاتر القضايا */
.filters {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.filterItem {
  flex: 1;
  min-width: 150px;
}

/* حاوية أدوات التحكم */
.controlsContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  background-color: white;
  padding: 10px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: 1px solid #e2e8f0;
  gap: 15px;
}

/* البحث */
.searchBox {
  position: relative;
  flex: 1;
  min-width: 280px;
  max-width: 500px;
}

.searchIcon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 16px;
}

.searchInput {
  width: 100%;
  padding: 8px 36px 8px 10px;
  border: 1.5px solid #e2e8f0;
  border-radius: var(--border-radius);
  font-size: 14px;
  background-color: #f8fafc;
  transition: var(--transition);
  height: 38px;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  background-color: white;
}

.viewToggle {
  display: flex;
  gap: 8px;
  align-items: center;
}

.viewButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f1f5f9;
  border: 1.5px solid #e2e8f0;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  transition: var(--transition);
  flex: 1;
  min-width: 120px;
  max-width: 160px;
  height: 38px;
}

.viewButton:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.viewButton.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* عرض البطاقات */
.casesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.caseCard,
.tableCard {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 16px;
  box-shadow: var(--box-shadow);
  border: 1px solid #e2e8f0;
  transition: var(--transition);
  cursor: pointer;
  animation: fadeIn 0.3s ease forwards;
}

.caseCard:hover,
.tableCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 10px;
}

.caseNumber {
  font-size: 15px;
  font-weight: 600;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  gap: 6px;
}

.caseIcon {
  color: var(--blue-medium-alt);
  font-size: 16px;
}

.reportDate {
  font-size: 13px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
}

.cardBody {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.infoGroup {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.infoIcon {
  color: #64748b;
  font-size: 16px;
}

.infoLabel {
  font-weight: 500;
  color: #64748b;
}

.infoValue {
  color: var(--dark-color);
}

.previewContainer {
  margin-top: 12px;
}

/* قائمة التقارير */
.reportsList {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* كل كارت تقرير */
.reportCard {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 15px;
  color: #334155;
  border: 1px solid #e2e8f0;
  box-shadow: var(--box-shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.reportCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

/* عنوان التقرير */
.reportTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

/* تفاصيل التقرير */
.reportDetails {
  font-size: 14px;
  margin-top: 5px;
  color: #64748b;
}

.reportPreview, .actionReportPreview {
  background-color: #f8fafc;
  padding: 12px;
  border-radius: var(--border-radius);
  border: 1px solid #e2e8f0;
}

.sectionTitle {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
  margin-top: 0px;
}

.reportText, .actionText {
  font-size: 13px;
  color: #334155;
  line-height: 1.5;
}

.actionText {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 6px;
}

.actionIcon {
  color: var(--secondary-color);
  font-size: 14px;
}

.linkedInfo {
  font-size: 12px;
  color: #b45309;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 6px;
  background-color: #fffbeb;
  padding: 4px 8px;
  border-radius: 4px;
}

.linkIcon {
  font-size: 12px;
}

/* تصميم قوائم الإجراءات والتأجيلات الجديدة */
.actionsSection, .deferralsSection {
  margin-bottom: 16px;
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.actionItem, .deferralItem {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: var(--transition);
}

.actionItem:hover, .deferralItem:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.itemContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.itemText {
  font-size: 13px;
  color: #334155;
  line-height: 1.4;
  font-weight: 500;
}

.itemDate {
  font-size: 12px;
  color: #64748b;
  font-weight: 400;
}

.deferralIcon {
  color: #f59e0b;
  font-size: 14px;
  margin-top: 2px;
}

.moreItems {
  font-size: 12px;
  color: #64748b;
  text-align: center;
  padding: 8px;
  background-color: #f1f5f9;
  border-radius: 6px;
  border: 1px dashed #cbd5e1;
  font-style: italic;
}

/* حالة عدم وجود قضايا */
.noCases {
  text-align: center;
  font-size: 16px;
  color: #64748b;
  padding: 40px;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: 1px solid #e2e8f0;
}

/* انيميشن */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* موبايل */
@media (max-width: 768px) {
  .contentContainer {
    padding: 12px;
    margin-top: 15px;
  }

  .pageTitle {
    font-size: 20px;
    padding: 10px;
  }

  .controls {
    padding: 10px;
  }

  .searchBox {
    width: 100%;
  }

  .controlsContainer {
    flex-direction: column;
    padding: 8px;
    gap: 10px;
  }

  .searchBox {
    min-width: 100%;
    max-width: 100%;
  }

  .viewToggle {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    gap: 5px;
  }

  .viewButton {
    flex: 1;
    min-width: 0;
    max-width: none;
    padding: 6px 4px;
    font-size: 12px;
    gap: 3px;
  }

  .casesGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .caseCard {
    padding: 16px;
  }

  .caseNumber {
    font-size: 15px;
  }

  .reportDate {
    font-size: 12px;
  }

  .infoGroup {
    font-size: 13px;
  }

  .infoIcon {
    font-size: 16px;
  }

  .sectionTitle {
    font-size: 13px;
  }

  .reportText, .actionText {
    font-size: 12px;
  }

  .linkedInfo {
    font-size: 11px;
  }

  .tableRow {
    font-size: 13px;
    padding: 6px 0;
  }

  .tableLabel {
    flex: 1.2;
  }

  .tableValue {
    flex: 1.8;
  }
}


.caseCard {
  border-radius: var(--border-radius);
  padding: 16px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  border-width: 1.5px;
  border-style: solid;
  background-color: white;
}

.caseCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.cardBorderPending {
  border-color: var(--blue-medium-alt); /* أزرق متوسط بديل لـ "قيد النظر" */
}

.cardBorderReport {
  border-color: var(--accent-gold); /* ذهبي لـ "محضر" */
}

.cardBorderLawsuit {
  border-color: var(--red-primary); /* بنفسجي داكن لـ "دعوى قضائية" */
}

.cardBorderDefault {
  border-color: #64748b; /* لون الإطار الافتراضي */
}


.casesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}
/* الحاوية الخاصة بالجدول */
.casesTableContainer {
  width: 100%;
  overflow-x: auto;
  margin: 0 auto;
  padding: 0;
  border-radius: var(--border-radius);
  background-color: white;
  box-shadow: var(--box-shadow);
  border: 1px solid #e2e8f0;
}

/* الجدول نفسه */
.casesTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background-color: white;
  color: #334155;
}

/* رأس الجدول */
.casesTable thead {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.casesTable th {
  padding: 12px 16px;
  text-align: center;
  font-weight: 600;
  color: #475569;
  white-space: nowrap;
}

/* صفوف الجدول */
.casesTable tbody tr {
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: background 0.2s ease;
}

.casesTable tbody tr:hover {
  background-color: #f1f5f9;
}

/* الخلايا */
.casesTable td {
  padding: 12px 16px;
  text-align: center;
  color: #334155;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ضبط عرض الأعمدة */
.casesTable td:nth-child(1) {
  width: 35%; /* اسم الموكل */
}

.casesTable td:nth-child(2) {
  width: 25%; /* رقم الدعوى */
}

.casesTable td:nth-child(3) {
  width: 40%; /* آخر ما تم */
}

/* تنسيق التاريخ */
.dateText {
  font-size: 12px;
  color: #64748b;
  display: block;
}

/* زر "فتح" */
.openButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: 12px;
  cursor: pointer;
  transition: var(--transition);
}

.openButton:hover {
  background-color: #2563eb;
}

/* تصميم متجاوب للهاتف */
@media (max-width: 600px) {
  .casesTable {
    font-size: 12px;
  }

  .casesTable th,
  .casesTable td {
    padding: 8px 6px;
  }

  .casesTable td:nth-child(1),
  .casesTable td:nth-child(2),
  .casesTable td:nth-child(3) {
    width: auto;
  }

  .casesTable td {
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 0;
  }

  .dateText {
    font-size: 10px;
  }

  .openButton {
    padding: 4px 8px;
    font-size: 11px;
  }
}

